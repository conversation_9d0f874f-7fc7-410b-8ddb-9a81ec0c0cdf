import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
    StyleSheet,
    Image,
    View,
    Alert,
    TouchableOpacity,
    Dimensions,
    Modal as ModalComponent,
    useWindowDimensions,
    ActivityIndicator,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicon from 'react-native-vector-icons/Ionicons';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import { TextInput, FlatList, ScrollView } from 'react-native-gesture-handler';
import DropDownPicker from 'react-native-dropdown-picker';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
// import DialogInput from 'react-native-dialog-input';
import * as User from '../util/User';
import LoginScreen from './LoginScreen';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import CheckBox from 'react-native-check-box';
// import { color } from 'react-native-reanimated';
import Close from 'react-native-vector-icons/AntDesign';
// import NumericInput from 'react-native-numeric-input';
import Styles from '../constant/Styles';
import moment, { isDate } from 'moment';
// import Barcode from 'react-native-barcode-builder';
// import Switch from 'react-native-switch-pro';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {
    getTransformForModalFullScreen,
    getTransformForModalInsideNavigation,
    getTransformForScreenInsideNavigation,
    isTablet
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { OUTLET_SHIFT_STATUS, SHIFT_PAY_TYPE, EXPAND_TAB_TYPE, USER_ORDER_STATUS, } from '../constant/common';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import { CommonStore } from '../store/commonStore';
import { openCashDrawer, printShiftReport, printShiftPayInOutReport } from '../util/printer';
// import { ActivityIndicator } from 'react-native-paper';
import { useNetInfo } from "@react-native-community/netinfo";
import APILocal from '../util/apiLocalReplacers';
import { PRINTER_USAGE_TYPE } from '../constant/printer';
import firestore from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';
import { NetPrinter } from '@conodene/react-native-thermal-receipt-printer-image-qr';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
// import { tr } from 'date-fns/locale';
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const SettingScreen = (props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const netInfo = useNetInfo();

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    const [selfCollect, setSelfCollect] = useState(true);
    const [openHourPickerVisible, setOpenHourPickerVisible] = useState(false);
    const [closeHourPickerVisible, setCloseHourPickerVisible] = useState(false);
    const [openHour, setOpenHour] = useState('');
    const [closeHour, setCloseHour] = useState('');
    const [isChecked2, setIsChecked2] = useState(false);
    const [isChecked3, setIsChecked3] = useState(false);
    const [isChecked4, setIsChecked4] = useState(false);
    const [isChecked5, setIsChecked5] = useState(false);
    const [isChecked6, setIsChecked6] = useState(false);
    const [isChecked7, setIsChecked7] = useState(false);
    const [isChecked8, setIsChecked8] = useState(false);
    const [isChecked9, setIsChecked9] = useState(false);
    const [isChecked10, setIsChecked10] = useState(false);
    const [isChecked11, setIsChecked11] = useState(false);
    const [isChecked12, setIsChecked12] = useState(false);
    const [isChecked13, setIsChecked13] = useState(false);
    const [value1, setValue1] = useState('');
    const [value2, setValue2] = useState('');
    const [amount, setAmount] = useState('');
    const [hourStart, setHourStart] = useState('');
    const [hourEnd, setHourEnd] = useState('');
    const [days, setDays] = useState(false);
    const [days1, setDays1] = useState(false);
    const [loading, setLoading] = useState(false);
    const [showDistance, setShowDistance] = useState('');
    const [expiryPeriod, setExpiryPeriod] = useState('');
    const [extentionCharges, setExtentionCharges] = useState('');
    const [extentionDuration, setExtentionDuration] = useState('');
    const [showDateTimePicker, setShowDateTimePicker] = useState(false);
    // const [pickerMode, setPickerMode] = useState('time');
    const [pickerMode, setPickerMode] = useState('datetime');
    const [merchantDisplay, setMerchantDisplay] = useState(false);
    const [shift, setShift] = useState(true);
    const [tax, setTax] = useState(false);
    const [sample, setSample] = useState(false);
    const [redemption, setRedemption] = useState(false);
    const [redemptionList, setRedemptionList] = useState(true);
    const [redemptionAdd, setRedemptionAdd] = useState(false);
    const [order, setOrder] = useState(false);
    const [previousState, setPreviousState] = useState(false);
    const [receipt, setReceipt] = useState([]);
    const [detail, setDetail] = useState([]);
    const [merchantInfo, setMerchantInfo] = useState([]);
    const [outlet, setOutlet] = useState([]);
    const [outletInfo, setOutletInfo] = useState([]);
    const [isModalVisible, setIsModalVisible] = useState(false);
    // const [outletId, setOutletId] = useState([]);
    const [merInfo, setMerInfo] = useState([]);
    // const [merchantId, setMerchantId] = useState([]);
    const [showKeypad, setShowKeypad] = useState(false);

    //New UI - Greg 2/3/2022
    const [showNotes, setShowNotes] = useState(false);
    const [deno, setDeno] = useState({
        cents5: 0,
        cents10: 0,
        cents20: 0,
        cents50: 0,
        rm1: 0,
        rm5: 0,
        rm10: 0,
        rm20: 0,
        rm50: 0,
        rm100: 0,
    });
    const [sumOfDeno, setSumOfDeno] = useState(0);
    useEffect(() => {
        if (isNaN(sumOfDeno)) {
            setSumOfDeno(0);
        }
        else {
            setSumOfDeno((
                (deno.cents5 * 0.05)
                + (deno.cents10 * 0.10)
                + (deno.cents20 * 0.20)
                + (deno.cents50 * 0.50)
                + (deno.rm1 * 1.00)
                + (deno.rm5 * 5.00)
                + (deno.rm10 * 10.00)
                + (deno.rm20 * 20.00)
                + (deno.rm50 * 50.00)
                + (deno.rm100 * 100.00)
            ));
        }
    }, [
        deno,
        // sumOfDeno,
    ]);

    const [showModal3, setShowModal3] = useState(false);
    const [showModal4, setShowModal4] = useState(false);
    const [showModalConfirm, setShowModalConfirm] = useState(false);
    const [closingAmount, setClosingAmount] = useState('');
    const [options, setOptions] = useState([]);
    const [shift1, setShift1] = useState([]);
    const [status, setStatus] = useState(false);
    const [logo, setLogo] = useState('');
    const [cover, setCover] = useState('');
    const [name, setName] = useState('');
    const [tname, setTname] = useState('');
    const [rate, setRate] = useState('');
    const [address, setAddress] = useState('');
    const [phone, setPhone] = useState('');
    const [payment, setPayment] = useState('');
    const [time, setTime] = useState('');
    const [statue, setStatue] = useState('');
    const [status1, setStatus1] = useState(false);
    const [outlets, setOutlets] = useState([]);
    const [outletId, setOutletId] = useState(null);
    const [myTextInput, setMyTextInput] = useState(React.createRef());
    const [start_time, setStart_time] = useState(false);
    const [end_time, setEnd_time] = useState(false);
    const [rev_time, setRev_time] = useState('');
    const [category, setCategory] = useState('');
    const [close, setClose] = useState('Closed');
    const [showNote, setShowNote] = useState(false);
    const [expandView, setExpandView] = useState(false);
    const [value, setValue] = useState('');
    const [extendOption, setExtendOption] = useState([
        { optionId: 1, price: 20, day: 7, days: false },
    ]);
    const [redemptionInfo, setRedemptionInfo] = useState([]);
    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
    const [alloutlet, setAlloutlet] = useState([]);
    const [discount, setDiscount] = useState('');
    const [amount1, setAmount1] = useState('');
    const [selectedCategoryId, setSelectedCategoryId] = useState('');
    const [categoryOutlet, setCategoryOutlet] = useState([]);
    const [extend, setExtend] = useState([]);
    const [outletss, setOutletss] = useState([]);
    const [redemptionDetail, setRedemptionDetail] = useState([]);
    const [outletInfo1, setOutletInfo1] = useState([]);
    const [category1, setCategory1] = useState([]);
    // const [merchantName, setMerchantName] = useState('');
    const [addOutletName, setAddOutletName] = useState('');
    const [addOutletWindow, setAddOutletWindow] = useState(false);
    const [taxList, setTaxList] = useState([]);
    const [note1, setNote1] = useState('');
    const [note2, setNote2] = useState('');
    const [note3, setNote3] = useState('');
    const [openings, setOpenings] = useState([]);
    const [editOpeningIndex, setEditOpeningIndex] = useState(0);

    const [shiftPayProceeding, setShiftPayProceeding] = useState(false);
    const [shiftPayType, setShiftPayType] = useState(SHIFT_PAY_TYPE.PAY_IN);
    const [shiftPayReason, setShiftPayReason] = useState('');

    //////////////////////////////////////////////////////////

    const outletPrinters = OutletStore.useState((s) => s.outletPrinters.concat(s.sunmiPrinters).concat(s.iminPrinters).concat(s.blePrinters));

    const currOutletShift = OutletStore.useState((s) => s.currOutletShift);
    const currOutletShiftStatus = OutletStore.useState(
        (s) => s.currOutletShiftStatus,
    );

    const currOutletId = MerchantStore.useState((s) => s.currOutletId);

    const merchantId = UserStore.useState((s) => s.merchantId);

    const currOutlet = MerchantStore.useState((s) => s.currOutlet);

    const allOutletShifts = OutletStore.useState(s => s.allOutletShifts);

    const userId = UserStore.useState((s) => s.firebaseUid);
    const userName = UserStore.useState((s) => s.name);
    const merchantName = MerchantStore.useState((s) => s.name);
    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );

    const isLoading = CommonStore.useState((s) => s.isLoading);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    const isIndividualShift = UserStore.useState((s) => s.isIndividualShift);

    const setState = () => { };

    // navigation.dangerouslyGetParent().setOptions({
    //     tabBarVisible: false,
    // });

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }
                }}
                style={styles.headerLeftStyle}>
                <Image
                    style={[{
                        width: 124,
                        height: 26,
                    }, switchMerchant ? {
                        transform: [
                            { scaleX: 0.7 },
                            { scaleY: 0.7 }
                        ],
                    } : {}]}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        ...global.getHeaderTitleStyle(),
                    },
                    // windowWidth >= 768 && switchMerchant
                    //     ? { right: windowWidth * 0.1 }
                    //     : {},
                    // windowWidth <= 768
                    //     ? { right: 20 }
                    //     : {},
                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Shift Settings
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }} />
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate('Setting');
                        }
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    const getRedemptionDetail = (param) => {
        ApiClient.GET(API.getOneSettingRedemption + param).then((result) => {
            setState({
                redemptionDetail: result,
                outletInfo1: result.outlet.name,
                category1: result.category.name,
                expiryPeriod: result.expiryPeriod,
                extentionCharges: result.extentionCharges,
                extentionDuration: result.extentionDuration,
            });
        });
    };

    // componentDidMount = () => {

    //   ApiClient.GET(API.getOutletByMerchant + User.getMerchantId()).then((result) => {
    //     setState({ outletInfo: result });
    //     result.map((element) => {
    //       setState({
    //         outletId: element.id,
    //         outletName: element.name,
    //         merchantName: element.merchant.name
    //       });
    //     });
    //   });

    //   ApiClient.GET(API.getAllSettingRedemption + User.getMerchantId()).then((result) => {
    //     setState({ redemptionInfo: result });
    //   });

    //   outlet()
    //   ApiClient.GET(API.getOutletCategory + User.getOutletId()).then((result) => {
    //     if (result !== undefined) {
    //       setState({ categoryOutlet: result });
    //     }

    //   });
    // }

    // function here
    const onCloseShiftBtn = (key) => {
        var decimal = closingAmount.split('.')[1];
        if (key >= 0 || key == '.') {
            if (closingAmount.includes('.'))
                if (closingAmount.length < 12 && decimal.length < 2)
                    // setState({ closingAmount: closingAmount + key });
                    setClosingAmount(closingAmount + key);
            if (!closingAmount.includes('.')) {
                if (closingAmount.length < 12)
                    // setState({ closingAmount: closingAmount + key });
                    setClosingAmount(closingAmount + key);
            }
        } else if (closingAmount.length > 0)
            // setState({ closingAmount: closingAmount.slice(0, key) });
            setClosingAmount(closingAmount.slice(0, key));
    };

    const getCurrentShift = (outletId) => {
        ApiClient.GET(API.getCurrentShift + outletId).then((result) => {
            setState({ shift1: result.success });
        });
        try {
            if (result.id != null) {
                Alert.alert(
                    '',
                    [
                        {
                            text: 'OK',
                            onPress: () => { },
                        },
                    ],
                    { cancelable: false },
                );
            }
        } catch (error) { }
    };

    const getTax = (outletId) => {
        ApiClient.GET(API.merchantTax + outletId).then((result) => {
            setState({ taxList: result });
        });
    };

    const closingShift = () => {
        var body = {
            outletId: User.getOutletId(),
            amount: closingAmount,
        };
        ApiClient.POST(API.closeShift, body, false).then((result) => {
            if (result) {
                Alert.alert(
                    'Success',
                    'Shift has been closed',
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                // _logout();

                                setState({
                                    showModal3: false,
                                    showKeypad: false,
                                    showModalConfirm: false,
                                });
                            },
                        },
                    ],
                    { cancelable: false },
                );
            }
        });
    };

    const getCurrentDate = () => {
        var displayDate = (currOutletShift && currOutletShift.openDate) ? currOutletShift.openDate : Date.now();

        var date = new Date(displayDate).getDate();
        var month = new Date(displayDate).getMonth() + 1;
        var year = new Date(displayDate).getFullYear();
        var hours = new Date(displayDate).getHours();
        var min = new Date(displayDate).getMinutes();
        var sec = new Date(displayDate).getSeconds();

        return (
            `${hours < 10 ? `0${hours}` : hours
            }:${min < 10 ? `0${min}` : min
            }:${sec < 10 ? `0${sec}` : sec
            }  ${date < 10 ? `0${date}` : date
            }-${month < 10 ? `0${month}` : month
            }-${year}`
        );
    };

    const _logout = async () => {
        await AsyncStorage.clear();
        User.setlogin(false);
        User.getRefreshMainScreen();
    };

    const openShift = () => {
        var body = {
            merchantId,
            outletId: currOutletId,
            amount: closingAmount.length > 0 ? closingAmount : '0',
            openDate: Date.now(),
            isIndividualShift: isIndividualShift,

            userId,
            userName,
            denomination: deno,
        };

        setShowKeypad(false);
        setShowModalConfirm(false);

        (
            // netInfo.isInternetReachable && netInfo.isConnected
            //     ?
            //     ApiClient.POST(API.openOutletShift, body, false)
            //     :
            (APILocal.openOutletShift({
                body,
                uid: userId,
            }))
        )
            .then((result) => {
                if (result && result.status === 'success') {
                    Alert.alert(
                        'Success',
                        'Shift has been opened',
                        [
                            {
                                text: 'OK',
                                onPress: () => {
                                    // _logout();
                                    // setState({
                                    //   showModal3: false,
                                    //   showKeypad: false,
                                    //   showModalConfirm: false,
                                    // });
                                    setShowModal3(false);


                                },
                            },
                        ],
                        { cancelable: false },
                    );
                }
            });
    };

    const closeShift = () => {
        var body = {
            merchantId,
            outletId: currOutletId,
            amount: closingAmount.length > 0 ? closingAmount : '0',
            closeDate: Date.now(),

            userId,
            userName,
            denomination: deno,
        };

        CommonStore.update(s => {
            s.isLoading = true;
        });

        setShowKeypad(false);
        setShowModalConfirm(false);
        (
            // netInfo.isInternetReachable && netInfo.isConnected
            //     ?
            //     ApiClient.POST(API.closeOutletShift, body, false)
            //     :
            (APILocal.closeOutletShift({
                body,
                uid: userId,
            }))
        )
            .then(async (result) => {
                if (result && result.status === 'success') {
                    console.log('RESULTS', result);
                    if (result.data) {
                        if (outletPrinters.length > 0) {
                            CommonStore.update(s => {
                                s.isLoading = false;
                            });

                            Alert.alert(
                                'Success',
                                `Shift has been closed${global.currOutlet.pSR ? ', shift report will be printed shortly.' : '.'}`,
                                [
                                    {
                                        text: 'OK',
                                        onPress: () => {
                                            // _logout();

                                            // setState({
                                            //   showModal3: false,
                                            //   showKeypad: false,
                                            //   showModalConfirm: false,
                                            // });
                                            setShowModal3(false);

                                            setDeno({
                                                cents5: 0,
                                                cents10: 0,
                                                cents20: 0,
                                                cents50: 0,
                                                rm1: 0,
                                                rm5: 0,
                                                rm10: 0,
                                                rm20: 0,
                                                rm50: 0,
                                                rm100: 0,
                                            });
                                        },
                                    },
                                ],
                                { cancelable: false },
                            );

                            var userOrdersResult = [];

                            const userOrderSnapshot = await firestore()
                                .collection(Collections.UserOrder)
                                .where('outletId', '==', result.data.outletId)
                                .where('orderStatus', '==', USER_ORDER_STATUS.ORDER_COMPLETED)
                                .where('createdAt', '>=', result.data.openDate)
                                .where('createdAt', '<', result.data.closeDate)
                                .get();

                            if (!userOrderSnapshot.empty) {
                                for (var i = 0; i < userOrderSnapshot.size; i++) {
                                    var record = userOrderSnapshot.docs[i].data();

                                    if (record.combinedOrderList &&
                                        record.combinedOrderList.length > 0) {
                                        // means this order already merged with other orders
                                    }
                                    else {
                                        userOrdersResult.push(record);
                                    }
                                }
                            }

                            // disconnectPrinter(printer); // no need anymore

                            if (global.currOutlet.pSR) {
                                await printShiftReport({
                                    outletName: currOutlet.name,
                                    outletAddress: currOutlet.address,
                                    outletPhone: currOutlet.phone,
                                    merchantName,
                                    waiterName: userName,

                                    outletShiftNum: allOutletShifts.filter(o => o.outletId === currOutlet.uniqueId).length,

                                    taxRate: currOutlet.taxRate,
                                    taxActive: currOutlet.taxActive,
                                    scRate: currOutlet.scRate,
                                    scActive: currOutlet.scActive,

                                    outletShift: result.data,

                                    userOrders: userOrdersResult,
                                },
                                    false,
                                    [PRINTER_USAGE_TYPE.RECEIPT],
                                    false,
                                    netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                                );
                            }
                        }
                        else {
                            CommonStore.update(s => {
                                s.isLoading = false;
                            });
                        }
                    }
                    else {
                        CommonStore.update(s => {
                            s.isLoading = false;
                        });
                    }
                }
                else {
                    CommonStore.update(s => {
                        s.isLoading = false;
                    });
                }
            });
    };

    const updateOutletShiftPayInOut = () => {
        var shiftPayDate = Date.now();

        var body = {
            merchantId,
            outletId: currOutletId,
            amount: closingAmount.length > 0 ? closingAmount : '0',

            shiftPayDate,

            shiftPayType,
            shiftPayReason,

            outletShiftId: currOutletShift.uniqueId,

            employeeId: userId,
            employeeName: userName,
        };

        setShowKeypad(false);
        setShowModalConfirm(false);

        (
            // netInfo.isInternetReachable && netInfo.isConnected
            //     ?
            //     ApiClient.POST(API.updateOutletShiftPayInOut, body, false)
            //     :
            (APILocal.updateOutletShiftPayInOut({
                body,
                uid: userId,
            }))
        )
            .then(async (result) => {
                if (result && result.status === 'success') {
                    Alert.alert(
                        'Success',
                        'Shift has been updated',
                        [
                            {
                                text: 'OK',
                                onPress: () => {
                                    // _logout();
                                    // setState({
                                    //   showModal3: false,
                                    //   showKeypad: false,
                                    //   showModalConfirm: false,
                                    // });
                                    setShowModal3(false);


                                },
                            },
                        ],
                        { cancelable: false },
                    );

                    var shiftPayInOut = {
                        amount: closingAmount.length > 0 ? parseFloat(closingAmount) : 0,

                        shiftPayDate,

                        shiftPayType,
                        shiftPayReason,
                    };

                    if (global.outletAutoPrintPaySlip) {
                        if (global.outletAutoPrintPaySlip) {
                            let paySlipPRNum = 1;
                            if (global.currOutlet.paySlipPRNum &&
                                global.currOutlet.paySlipPRNum !== undefined) {
                                paySlipPRNum = global.currOutlet.paySlipPRNum;
                            }

                            for (let indexPR = 0; indexPR < paySlipPRNum; indexPR++) {
                                await printShiftPayInOutReport({
                                    outletName: currOutlet.name,
                                    outletAddress: currOutlet.address,
                                    outletPhone: currOutlet.phone,
                                    merchantName,
                                    waiterName: userName,

                                    outletShiftNum: allOutletShifts.filter(o => o.outletId === currOutlet.uniqueId).length,

                                    taxRate: currOutlet.taxRate,
                                    taxActive: currOutlet.taxActive,
                                    scRate: currOutlet.scRate,
                                    scActive: currOutlet.scActive,

                                    // outletShift: result.data,
                                    outletShift: currOutletShift,

                                    userOrders: [],

                                    shiftPayInOut,
                                },
                                    false,
                                    [PRINTER_USAGE_TYPE.RECEIPT],
                                    false,
                                    netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                                );
                            }
                        }
                    }
                }

                setShiftPayProceeding(false);
                setShiftPayReason('');
            });
    };

    // function end

    const week = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday',
    ];

    return (
        // <View style={styles.container}>
        //   <View style={styles.sidebar}>
        (<UserIdleWrapper disabled={!isMounted}>
            <View
                style={[
                    styles.container,
                    !isTablet()
                        ? {
                            transform: [{ scaleX: 1 }, { scaleY: 1 }],
                        }
                        : {},
                    {
                        ...getTransformForScreenInsideNavigation(),
                    }
                ]}>
                {/* <View
                    style={[
                        styles.sidebar,
                        !isTablet()
                            ? {
                                width: windowWidth * 0.09,
                            }
                            : {},
                        switchMerchant
                            ? {
                                // width: '10%'
                            }
                            : {},
                        {
                            width: windowWidth * 0.08,
                        }
                    ]}>
                    <SideBar
                        navigation={props.navigation}
                        selectedTab={10}
                        expandSettings
                    />
                </View> */}

                <ScrollView
                    showsVerticalS
                    crollIndicator={false}
                    scrollEnabled={switchMerchant || windowHeight < 670}
                    style={{}}
                    contentContainerStyle={[
                        !isTablet()
                            ? { height: windowHeight * 1.13 }
                            : {
                                paddingBottom: windowHeight * 0.025,
                                backgroundColor: Colors.highlightColor,
                                height: windowHeight
                                // borderWidth:10,
                            },
                    ]}>
                    <ScrollView horizontal>
                        <View
                            style={[
                                styles.content,
                                !isTablet()
                                    ? {
                                        paddingBottom: windowHeight * 1,
                                    }
                                    : {},
                                switchMerchant
                                    ? {
                                        height: windowHeight * 1.2,
                                    }
                                    : { height: windowHeight * 0.83 },
                                {
                                    width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
                                }
                            ]}>
                            {/* <View style={{flexDirection: 'row', marginBottom: 10}}>
            <View style={{flexDirection: 'row', flex: 1}}></View>
            <TextInput
              editable={!loading}
              underlineColorAndroid={Colors.whiteColor}
              clearButtonMode="while-editing"
              style={styles.textInput}
              placeholder="search"
              onChangeText={(text) => {
                setState({search: text.trim()});
              }}
              value={email}
            />
          </View> */}
                            {/* <View
            style={{
              flexDirection: 'row',
              backgroundColor: Colors.highlightColor,
              padding: 12,
            }}>
            <TouchableOpacity
              onPress={() => {
                setState({
                  merchantDisplay: true,
                  shift: false,
                  tax: false,
                  sample: false,
                  redemption: false,
                  order: false,
                });
              }}>
              <Text>General</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  shift: true,
                  merchantDisplay: false,
                  tax: false,
                  sample: false,
                  redemption: false,
                  order: false,
                });
                getCurrentShift(User.getOutletId());
              }}>
              <Text style={{ marginLeft: 30 }}>Shift</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  tax: true,
                  merchantDisplay: false,
                  shift: false,
                  sample: false,
                  redemption: false,
                  order: false,
                });
                getTax(User.getOutletId())
              }}>
              <Text style={{ marginLeft: 30 }}>Tax</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  sample: true,
                  tax: false,
                  merchantDisplay: false,
                  shift: false,
                  redemption: false,
                  order: false,
                });
                setState({
                  showNote: false,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Sample Receipt</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  merchantDisplay: false,
                  shift: false,
                  tax: false,
                  sample: false,
                  redemption: false,
                  order: true,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Order</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  merchantDisplay: false,
                  shift: false,
                  tax: false,
                  sample: false,
                  redemption: true,
                  order: false,
                  redemptionList: true,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Redemption</Text>
            </TouchableOpacity>
          </View> */}

                            {/* <TouchableOpacity style={{ width: '10%' }}
              //   onPress={() => { props.navigation.goBack(); }}>
              onPress={() => { props.navigation.goBack(); }}>

              <View style={{
                  paddingBottom: 5,
                  //marginLeft: 10,
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'flex-start',
              }}>
                  <Icon
                      name="chevron-left"
                      size={30}
                      color={Colors.primaryColor}
                      style={{
                      }}
                  />

                  <Text
                      style={{
                          color: Colors.primaryColor,
                          fontSize: 17,
                          textAlign: 'center',
                          fontFamily: 'NunitoSans-Bold',
                      }}>
                      Back
                  </Text>
              </View>
          </TouchableOpacity> */}
                            <View
                                style={[
                                    !isTablet()
                                        ? {
                                            height: '110%',
                                            backgroundColor: Colors.whiteColor,
                                            // borderWidth1,
                                            // flex: 1,
                                            flexShrink: 0,
                                            minHeight: '85%',
                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 3,
                                            borderRadius: 5,
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            paddingBottom: 25,
                                        }
                                        : {
                                            backgroundColor: Colors.whiteColor,
                                            // borderWidth:1,
                                            //flex: 1,
                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            // elevation: 1,
                                            elevation: 3,
                                            borderRadius: 5,
                                            // borderRadius: 8,
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            height: !switchMerchant && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? windowHeight * 0.85 : windowHeight * 0.77,
                                        },
                                ]}>
                                {
                                    isLoading
                                        ?
                                        <View style={{
                                            width: '100%',
                                            height: '100%',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                        }}>
                                            <ActivityIndicator size={'large'} color={Colors.primaryColor} />
                                        </View>
                                        :
                                        <View
                                            style={{
                                                flex: 1,
                                                alignSelf: 'center',
                                                justifyContent: 'center',
                                                marginBottom: 10,
                                            }}>
                                            {currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED && (
                                                <View
                                                    style={[
                                                        styles.shiftView,
                                                        {
                                                            height: switchMerchant ? 50 : !switchMerchant && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? 50 : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 50 : 60),
                                                            // marginTop: switchMerchant ? 50 : '8%',
                                                        },
                                                    ]}>
                                                    <Text
                                                        style={[
                                                            styles.shiftText,
                                                            { fontSize: switchMerchant ? 20 : (windowHeight < 610 ? 20 : 25) },
                                                        ]}>
                                                        SHIFT OPEN
                                                    </Text>
                                                </View>
                                            )}

                                            {currOutletShiftStatus === OUTLET_SHIFT_STATUS.CLOSED && (
                                                <View
                                                    style={[
                                                        styles.shiftView,
                                                        {
                                                            height: switchMerchant ? 50 : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 50 : 60),
                                                            // marginTop: switchMerchant ? 50 : '8%',
                                                        },
                                                    ]}>
                                                    <Text
                                                        style={[
                                                            styles.shiftText,
                                                            { fontSize: switchMerchant ? 20 : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 20 : 25) },
                                                        ]}>
                                                        SHIFT CLOSED
                                                    </Text>
                                                </View>
                                            )}

                                            <View
                                                style={{
                                                    alignSelf: 'center',
                                                    alignItems: 'center',
                                                    width: 200,
                                                    height: 40,
                                                    marginTop: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 5 : 10,
                                                }}>
                                                <Text
                                                    style={{
                                                        fontSize: switchMerchant ? 20 : !switchMerchant && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? 20 : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 20 : 30),
                                                        fontWeight: 'bold',
                                                    }}>
                                                    Powered by
                                                </Text>
                                            </View>

                                            <View
                                                style={{
                                                    alignSelf: 'center',
                                                    width: switchMerchant ? 200 : !switchMerchant && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? 200 : (windowWidth < 1000 ? 200 : 250),
                                                    height: switchMerchant ? 60 : !switchMerchant && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? 60 : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 60 : 80),
                                                    marginTop: switchMerchant ? '0.1%' : '0.1%',
                                                }}>
                                                <Image
                                                    style={styles.headerLogo1}
                                                    resizeMode="contain"
                                                    source={require('../assets/image/logo_2.png')}
                                                />
                                            </View>
                                            <View
                                                style={{
                                                    alignSelf: 'center',
                                                    //width: '100%'

                                                    //width: windowWidth / 2
                                                }}>
                                                <Text
                                                    style={[
                                                        styles.logoTxt,
                                                        {
                                                            fontSize: switchMerchant ? 15 : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 15 : 20),
                                                            marginBottom: switchMerchant ? 5 : !switchMerchant && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? 5 : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 10 : 20),
                                                            width: windowWidth / 2,
                                                            textAlign: 'center'
                                                        }
                                                    ]}>
                                                    Unlimited Perks
                                                </Text>
                                            </View>
                                            {currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED && (
                                                <>
                                                    {/* <View
                  style={{
                    alignSelf: 'center',
                    alignItems: 'center',
                    width: 200,
                    height: 40,
                  }}
                >
                  <Text style={{ fontSize: switchMerchant ? 20 : 30, fontWeight: 'bold' }}>
                    Powered by
                  </Text>
                </View> */}
                                                    {/* <View
                  style={{
                    alignSelf: 'center',
                    alignItems: 'center',
                    width: 200,
                    height: 60,
                    marginBottom: '3%',
                  }}>
                  <Text style={{ fontSize: 50, fontWeight: 'bold' }}>OPEN</Text>
                </View> */}
                                                    <View
                                                        style={{
                                                            alignSelf: 'center',
                                                            alignItems: 'center',
                                                            width: 200,
                                                            height: !switchMerchant && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350 ? 35 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 35 : 50,
                                                        }}>
                                                        <Text style={{ fontSize: switchMerchant ? 15 : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 15 : 20) }}>
                                                            Open at
                                                        </Text>
                                                    </View>
                                                    <View
                                                        style={{
                                                            alignSelf: 'center',
                                                            alignItems: 'center',
                                                        }}>
                                                        <View
                                                            style={[
                                                                styles.textInput5,
                                                                { height: switchMerchant ? 48 : 50 },
                                                            ]}>
                                                            <Text
                                                                style={{
                                                                    paddingVertical: 16,
                                                                    alignSelf: 'center',
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                }}>
                                                                {getCurrentDate()}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                    <View style={{
                                                        alignSelf: 'center',
                                                        //alignItems: 'center',
                                                        width: 300,
                                                    }}>
                                                        <TouchableOpacity
                                                            style={{
                                                                justifyContent: 'center',
                                                                flexDirection: 'row',
                                                                borderWidth: 1,
                                                                borderColor: Colors.primaryColor,
                                                                backgroundColor: '#4E9F7D',
                                                                borderRadius: 5,
                                                                //width: 160,
                                                                paddingHorizontal: 10,
                                                                height: switchMerchant ? 35 : 40,
                                                                alignItems: 'center',
                                                                shadowOffset: {
                                                                    width: 0,
                                                                    height: 2,
                                                                },
                                                                shadowOpacity: 0.22,
                                                                shadowRadius: 3.22,
                                                                elevation: 1,
                                                                zIndex: -1,
                                                                marginTop: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 10 : 20,
                                                            }}
                                                            onPress={async () => {
                                                                // setState({ showModalConfirm: true });

                                                                // await openCashDrawer();

                                                                setShiftPayProceeding(false);

                                                                setShowModalConfirm(true);

                                                                // if (outletPrinters.length > 0) {
                                                                //   await openCashDrawer();
                                                                // }

                                                                setDeno({
                                                                    cents5: 0,
                                                                    cents10: 0,
                                                                    cents20: 0,
                                                                    cents50: 0,
                                                                    rm1: 0,
                                                                    rm5: 0,
                                                                    rm10: 0,
                                                                    rm20: 0,
                                                                    rm50: 0,
                                                                    rm100: 0,
                                                                });
                                                                setSumOfDeno(0);
                                                                setClosingAmount('');
                                                            }}>
                                                            <Text
                                                                style={{
                                                                    color: Colors.whiteColor,
                                                                    marginLeft: 5,
                                                                    fontSize: switchMerchant ? 10 : 16,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                CLOSE REGISTER
                                                            </Text>
                                                        </TouchableOpacity>
                                                    </View>
                                                </>
                                            )}

                                            {currOutletShiftStatus === OUTLET_SHIFT_STATUS.CLOSED && (
                                                <>
                                                    <View
                                                        style={{
                                                            alignSelf: 'center',
                                                            alignItems: 'center',
                                                            width: 300,
                                                            height: 40,
                                                        }} />
                                                    <View style={{}}>
                                                        <TouchableOpacity
                                                            style={{
                                                                justifyContent: 'center',
                                                                flexDirection: 'row',
                                                                borderWidth: 1,
                                                                borderColor: Colors.primaryColor,
                                                                backgroundColor: '#4E9F7D',
                                                                borderRadius: 5,
                                                                //width: 160,
                                                                paddingHorizontal: 10,
                                                                height: switchMerchant ? 35 : 40,
                                                                alignItems: 'center',
                                                                shadowOffset: {
                                                                    width: 0,
                                                                    height: 2,
                                                                },
                                                                shadowOpacity: 0.22,
                                                                shadowRadius: 3.22,
                                                                elevation: 1,
                                                                zIndex: -1,
                                                                // marginTop: 20,
                                                            }}
                                                            onPress={async () => {
                                                                // setState({ showModalConfirm: true });

                                                                // await openCashDrawer();

                                                                setShiftPayProceeding(false);

                                                                if (global.currOutlet.openShiftWithNum) { //change this to false to use other open modal
                                                                    setShowModalConfirm(true);
                                                                } else {
                                                                    setShowNotes(true);
                                                                }
                                                                // if (outletPrinters.length > 0) {
                                                                //   await openCashDrawer();
                                                                // }

                                                                setDeno({
                                                                    cents5: 0,
                                                                    cents10: 0,
                                                                    cents20: 0,
                                                                    cents50: 0,
                                                                    rm1: 0,
                                                                    rm5: 0,
                                                                    rm10: 0,
                                                                    rm20: 0,
                                                                    rm50: 0,
                                                                    rm100: 0,
                                                                });
                                                                setSumOfDeno(0);
                                                                setClosingAmount('');
                                                            }}>
                                                            <Text
                                                                style={{
                                                                    color: Colors.whiteColor,
                                                                    //marginLeft: 5,
                                                                    fontSize: switchMerchant ? 10 : 16,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                OPEN REGISTER
                                                            </Text>
                                                        </TouchableOpacity>
                                                    </View>
                                                </>
                                            )}

                                            {
                                                (currOutletShift && currOutletShift.uniqueId && currOutletShift.closeDate === null)
                                                    ?
                                                    <View style={{
                                                        flexDirection: 'row',
                                                        alignSelf: 'center',

                                                        justifyContent: 'space-between',

                                                        width: 300

                                                        // marginTop: currOutletShiftStatus === OUTLET_SHIFT_STATUS.CLOSED ? '-20%' : 0,
                                                    }}>
                                                        <TouchableOpacity
                                                            style={{
                                                                justifyContent: 'center',
                                                                flexDirection: 'row',
                                                                borderWidth: 1,
                                                                borderColor: Colors.tabCyan,
                                                                backgroundColor: Colors.tabCyan,
                                                                borderRadius: 5,
                                                                //width: 160,
                                                                paddingHorizontal: 10,
                                                                height: switchMerchant ? 35 : 40,
                                                                alignItems: 'center',
                                                                shadowOffset: {
                                                                    width: 0,
                                                                    height: 2,
                                                                },
                                                                shadowOpacity: 0.22,
                                                                shadowRadius: 3.22,
                                                                elevation: 1,
                                                                zIndex: -1,
                                                                marginTop: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 10 : 20,

                                                                width: 120,
                                                            }}
                                                            onPress={async () => {
                                                                // setState({ showModalConfirm: true });

                                                                // await openCashDrawer();

                                                                setShiftPayProceeding(true);

                                                                setShiftPayType(SHIFT_PAY_TYPE.PAY_IN);

                                                                setShowModalConfirm(true);

                                                                setDeno({
                                                                    cents5: 0,
                                                                    cents10: 0,
                                                                    cents20: 0,
                                                                    cents50: 0,
                                                                    rm1: 0,
                                                                    rm5: 0,
                                                                    rm10: 0,
                                                                    rm20: 0,
                                                                    rm50: 0,
                                                                    rm100: 0,
                                                                });

                                                                // if (outletPrinters.length > 0) {
                                                                //   await openCashDrawer();
                                                                // }
                                                            }}>
                                                            <Text
                                                                style={{
                                                                    color: Colors.whiteColor,
                                                                    marginLeft: 5,
                                                                    fontSize: switchMerchant ? 10 : 16,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                PAY IN
                                                            </Text>
                                                        </TouchableOpacity>

                                                        <TouchableOpacity
                                                            style={{
                                                                justifyContent: 'center',
                                                                flexDirection: 'row',
                                                                borderWidth: 1,
                                                                borderColor: Colors.tabRed,
                                                                backgroundColor: Colors.tabRed,
                                                                borderRadius: 5,
                                                                //width: 160,
                                                                paddingHorizontal: 10,
                                                                height: switchMerchant ? 35 : 40,
                                                                alignItems: 'center',
                                                                shadowOffset: {
                                                                    width: 0,
                                                                    height: 2,
                                                                },
                                                                shadowOpacity: 0.22,
                                                                shadowRadius: 3.22,
                                                                elevation: 1,
                                                                zIndex: -1,
                                                                marginTop: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 10 : 20,

                                                                width: 120,
                                                            }}
                                                            onPress={async () => {
                                                                // setState({ showModalConfirm: true });

                                                                // await openCashDrawer();

                                                                setShiftPayProceeding(true);

                                                                setShiftPayType(SHIFT_PAY_TYPE.PAY_OUT);

                                                                setShowModalConfirm(true);

                                                                setDeno({
                                                                    cents5: 0,
                                                                    cents10: 0,
                                                                    cents20: 0,
                                                                    cents50: 0,
                                                                    rm1: 0,
                                                                    rm5: 0,
                                                                    rm10: 0,
                                                                    rm20: 0,
                                                                    rm50: 0,
                                                                    rm100: 0,
                                                                });

                                                                // if (outletPrinters.length > 0) {
                                                                //   await openCashDrawer();
                                                                // }
                                                            }}>
                                                            <Text
                                                                style={{
                                                                    color: Colors.whiteColor,
                                                                    marginLeft: 5,
                                                                    fontSize: switchMerchant ? 10 : 16,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                PAY OUT
                                                            </Text>
                                                        </TouchableOpacity>
                                                    </View>
                                                    :
                                                    <></>
                                            }

                                            <ModalView
                                                supportedOrientations={['landscape', 'portrait']}
                                                style={{
                                                    backgroundColor: Colors.primaryColor,
                                                    padding: 20,
                                                    margin: 30,
                                                }}
                                                animationType="fade"
                                                transparent
                                                visible={showModalConfirm}
                                                onRequestClose={() => {
                                                    Alert.alert(
                                                        'Modal is closing',
                                                        [{ text: 'OK', onPress: () => { } }],
                                                        { cancelable: false },
                                                    );
                                                }}>
                                                <View
                                                    style={{
                                                        backgroundColor: 'rgba(0,0,0,0.5)',
                                                        flex: 1,
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                    }}>
                                                    <View
                                                        style={{
                                                            width: shiftPayProceeding ? 500 : 350,
                                                            height: shiftPayProceeding ? 320 : 270,
                                                            backgroundColor: 'white',
                                                            borderRadius: 12,
                                                            alignItems: 'center',
                                                            justifyContent: 'space-around',

                                                            ...getTransformForModalInsideNavigation(),
                                                        }}>
                                                        <View
                                                            style={{
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                            }}>
                                                            <Text
                                                                style={{
                                                                    fontWeight: 'bold',
                                                                    fontSize: switchMerchant ? 16 : 20,
                                                                    marginBottom: 20,
                                                                }}>
                                                                Confirm
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    color: Colors.fieldtTxtColor,
                                                                    fontSize: switchMerchant ? 15 : 16,
                                                                    marginBottom: 10,
                                                                }}>
                                                                {
                                                                    shiftPayProceeding
                                                                        ?
                                                                        (`Please enter the reason to proceed`)
                                                                        :
                                                                        (`Are you sure you want to ${currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED
                                                                            ? 'close'
                                                                            : 'open'
                                                                            }?`)
                                                                }
                                                            </Text>

                                                            {
                                                                (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED &&
                                                                    (!global.outletToggleOpenOrder &&
                                                                        (!currOutlet.toggleOpenOrder)
                                                                    ) && !shiftPayProceeding
                                                                )
                                                                    ?
                                                                    <Text
                                                                        style={{
                                                                            color: Colors.fieldtTxtColor,
                                                                            fontSize: switchMerchant ? 15 : 16,
                                                                            marginBottom: 10,

                                                                            textAlign: 'center',
                                                                        }}>
                                                                        {
                                                                            `Note: This will checkout all existing Dine In orders.`
                                                                        }
                                                                    </Text>
                                                                    :
                                                                    <></>
                                                            }
                                                        </View>

                                                        {
                                                            shiftPayProceeding
                                                                ?
                                                                <View
                                                                    style={{
                                                                        alignItems: 'center',
                                                                        flexDirection: 'row',
                                                                        width: 300,
                                                                    }}>
                                                                    <TextInput
                                                                        editable={!loading}
                                                                        underlineColorAndroid={Colors.whiteColor}
                                                                        clearButtonMode="while-editing"
                                                                        style={{
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                            width: 300,
                                                                            height: 50,
                                                                            backgroundColor: Colors.fieldtBgColor,
                                                                            borderRadius: 10,
                                                                            marginRight: 0,
                                                                            flex: 1,
                                                                            flexDirection: 'row',
                                                                            textAlign: 'center',
                                                                        }}
                                                                        placeholder="Reason"
                                                                        onChangeText={(text) => {
                                                                            setShiftPayReason(text);
                                                                        }}
                                                                        defaultValue={shiftPayReason}
                                                                    />
                                                                </View>
                                                                :
                                                                <></>
                                                        }

                                                        <View
                                                            style={{
                                                                alignItems: 'center',
                                                                flexDirection: 'row',
                                                            }}>
                                                            <TouchableOpacity
                                                                onPress={async () => {
                                                                    // setState({
                                                                    //   showKeypad: true,
                                                                    // });

                                                                    // CommonStore.update(s => {
                                                                    //   s.isLoading = true;
                                                                    // });

                                                                    // if (outletPrinters.length > 0) {
                                                                    //   await openCashDrawer();
                                                                    // }

                                                                    // CommonStore.update(s => {
                                                                    //   s.isLoading = false;
                                                                    // });

                                                                    setShowModalConfirm(false);

                                                                    if (shiftPayProceeding) {
                                                                        if (!shiftPayReason) {
                                                                            Alert.alert('Info', 'Please fill in the reason box.');

                                                                            return;
                                                                        }

                                                                        if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.CLOSED) {
                                                                            setClosingAmount('');

                                                                            setTimeout(() => {
                                                                                setShowKeypad(true);
                                                                            }, 1000);

                                                                            if (outletPrinters.length > 0) {
                                                                                await openCashDrawer();
                                                                            }
                                                                        }
                                                                        else {
                                                                            setClosingAmount('');

                                                                            setTimeout(() => {
                                                                                setShowNotes(true);
                                                                            }, 1000);

                                                                            if (outletPrinters.length > 0) {
                                                                                await openCashDrawer();
                                                                            }
                                                                        }
                                                                    }
                                                                    else if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.CLOSED) {
                                                                        setClosingAmount('');

                                                                        setTimeout(() => {
                                                                            setShowKeypad(true);
                                                                        }, 1000);

                                                                        if (outletPrinters.length > 0) {
                                                                            await openCashDrawer();
                                                                        }
                                                                    }
                                                                    else {
                                                                        setClosingAmount('');

                                                                        setTimeout(() => {
                                                                            setShowNotes(true);
                                                                        }, 1000);

                                                                        if (outletPrinters.length > 0) {
                                                                            await openCashDrawer();
                                                                        }
                                                                    }
                                                                }}>
                                                                <View
                                                                    style={{
                                                                        backgroundColor: !options
                                                                            ? Colors.whiteColor
                                                                            : Colors.primaryColor,
                                                                        justifyContent: 'center',
                                                                        flexDirection: 'row',
                                                                        borderWidth: 1,
                                                                        borderColor: !options
                                                                            ? Colors.whiteColor
                                                                            : Colors.primaryColor,
                                                                        borderRadius: 5,
                                                                        width: 100,
                                                                        paddingHorizontal: 10,
                                                                        height: 40,
                                                                        alignItems: 'center',
                                                                        shadowOffset: {
                                                                            width: 0,
                                                                            height: 2,
                                                                        },
                                                                        shadowOpacity: 0.22,
                                                                        shadowRadius: 3.22,
                                                                        elevation: 1,
                                                                        zIndex: -1,
                                                                        marginRight: 15,
                                                                    }}>
                                                                    <Text
                                                                        style={{
                                                                            color: !options
                                                                                ? Colors.fieldtTxtColor
                                                                                : Colors.whiteColor,
                                                                            fontSize: switchMerchant ? 15 : 16,
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                        }}>
                                                                        {shiftPayProceeding ? 'OK' : 'YES'}
                                                                    </Text>
                                                                </View>
                                                            </TouchableOpacity>
                                                            <TouchableOpacity
                                                                onPress={() => {
                                                                    // setState({
                                                                    //   showKeypad: false,
                                                                    //   showModalConfirm: false,
                                                                    // });

                                                                    setShiftPayProceeding(false);

                                                                    setShowKeypad(false);
                                                                    setShowModalConfirm(false);
                                                                }}>
                                                                <View
                                                                    style={{
                                                                        backgroundColor: !options
                                                                            ? Colors.primaryColor
                                                                            : Colors.fieldtBgColor,
                                                                        justifyContent: 'center',
                                                                        flexDirection: 'row',
                                                                        borderWidth: 1,
                                                                        borderColor: !options
                                                                            ? Colors.primaryColor
                                                                            : Colors.fieldtBgColor,
                                                                        borderRadius: 5,
                                                                        width: 100,
                                                                        paddingHorizontal: 10,
                                                                        height: 40,
                                                                        alignItems: 'center',
                                                                        shadowOffset: {
                                                                            width: 0,
                                                                            height: 2,
                                                                        },
                                                                        shadowOpacity: 0.22,
                                                                        shadowRadius: 3.22,
                                                                        elevation: 1,
                                                                        zIndex: -1,
                                                                    }}>
                                                                    <Text
                                                                        style={{
                                                                            color: options
                                                                                ? Colors.fieldtTxtColor
                                                                                : Colors.primaryColor,
                                                                            // color: fieldtTxtColor,
                                                                            fontSize: switchMerchant ? 15 : 16,
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                        }}>
                                                                        CANCEL
                                                                    </Text>
                                                                </View>
                                                            </TouchableOpacity>
                                                        </View>
                                                    </View>
                                                </View>
                                            </ModalView>

                                            <ModalView
                                                supportedOrientations={['landscape', 'portrait']}
                                                style={{
                                                    // flex: 1,
                                                    backgroundColor: Colors.primaryColor,
                                                    padding: 40,
                                                    margin: 50,
                                                    borderRadius: 12,

                                                    // ...getTransformForModalInsideNavigation(),
                                                }}
                                                animationType="fade"
                                                transparent={false}
                                                visible={showKeypad}
                                                onRequestClose={() => {
                                                    Alert.alert(
                                                        'Modal is closing',
                                                        [{ text: 'OK', onPress: () => { } }],
                                                        { cancelable: false },
                                                    );
                                                }}>
                                                <View style={{
                                                    // backgroundColor: 'red',

                                                    width: windowWidth,
                                                    height: windowHeight,

                                                    ...getTransformForModalFullScreen(),
                                                }}>
                                                    <ScrollView
                                                        showsVerticalScrollIndicator={false}
                                                        scrollEnabled={Dimensions.get('screen').height < 670}
                                                        style={{
                                                        }}
                                                        contentContainerStyle={{
                                                            paddingBottom: windowHeight * 0.1,
                                                            backgroundColor: Colors.whiteColor,

                                                            // ...getTransformForModalInsideNavigation(),
                                                        }}
                                                    >
                                                        {/* <ScrollView horizontal={true}> */}
                                                        <View
                                                            style={[
                                                                styles.container,
                                                                {
                                                                    flexDirection: 'column',
                                                                    justifyContent: 'center',
                                                                    alignItems: 'center',
                                                                    alignContent: 'center',

                                                                    // ...getTransformForModalInsideNavigation(),
                                                                },
                                                            ]}>
                                                            <TouchableOpacity
                                                                style={{
                                                                    // width: '10%',
                                                                    // backgroundColor: 'red',
                                                                    position: 'absolute',
                                                                    top: '3%',
                                                                    left: '2%',
                                                                }}
                                                                //   onPress={() => { props.navigation.goBack(); }}>
                                                                onPress={() => {
                                                                    // goBackButton();
                                                                    setShowModalConfirm(false);
                                                                    setShowKeypad(false);

                                                                    setShiftPayProceeding(false);
                                                                    setShiftPayReason('');
                                                                }}>
                                                                <View
                                                                    style={{
                                                                        paddingTop: 10,
                                                                        marginBottom: 10,
                                                                        display: 'flex',
                                                                        flexDirection: 'row',
                                                                        alignItems: 'center',
                                                                        justifyContent: 'flex-start',
                                                                    }}>
                                                                    <Icon
                                                                        name="chevron-left"
                                                                        size={switchMerchant ? 20 : 30}
                                                                        style={{
                                                                            color: Colors.primaryColor,
                                                                            alignSelf: 'center',
                                                                        }}
                                                                    />
                                                                    <Text
                                                                        style={{
                                                                            fontFamily: 'Nunitosans-Bold',
                                                                            color: Colors.primaryColor,
                                                                            fontSize: switchMerchant ? 14 : 17,
                                                                            marginBottom: Platform.OS === 'ios' ? 0 : 1,
                                                                        }}>
                                                                        Back
                                                                    </Text>
                                                                </View>
                                                            </TouchableOpacity>
                                                            <View
                                                                style={{
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    paddingTop: windowHeight * 0.1,

                                                                    width: windowWidth / 2
                                                                }}>
                                                                <Text
                                                                    style={{
                                                                        // fontSize: switchMerchant ? 30 : 45,
                                                                        fontSize: 0.63 * (Dimensions.get('screen').width * 0.05),
                                                                        fontWeight: 'bold',
                                                                        margin: switchMerchant ? 1 : 12,
                                                                    }}>
                                                                    {
                                                                        shiftPayProceeding
                                                                            ?
                                                                            (shiftPayType === SHIFT_PAY_TYPE.PAY_IN ? 'Pay In' : 'Pay Out')
                                                                            :
                                                                            (`${currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED
                                                                                ? 'Close'
                                                                                : 'Open'
                                                                                } Shift`)
                                                                    }
                                                                </Text>
                                                                <Text
                                                                    style={{
                                                                        // fontSize: switchMerchant ? 12 : 20,
                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                        color: Colors.fieldtTxtColor,
                                                                        width: windowWidth / 2,
                                                                        textAlign: 'center'
                                                                    }}>
                                                                    {
                                                                        shiftPayProceeding
                                                                            ?
                                                                            (shiftPayType === SHIFT_PAY_TYPE.PAY_IN ? 'Pay In Amount' : 'Pay Out Amount')
                                                                            :
                                                                            (`${currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED
                                                                                ? 'Closing'
                                                                                : 'Opening'
                                                                                } Amount`)
                                                                    }
                                                                </Text>
                                                            </View>
                                                            <View style={{ justifyContent: 'center', width: windowWidth / 2 }}>
                                                                <Text style={{
                                                                    // fontSize: switchMerchant ? 30 : 100
                                                                    textAlign: 'center',
                                                                    fontSize: 1.59 * (Dimensions.get('screen').width * 0.05),
                                                                    width: windowWidth / 2
                                                                }}>
                                                                    {closingAmount.length === 0 ? '0' : closingAmount}
                                                                </Text>
                                                            </View>
                                                            {/* Numpad */}
                                                            <View>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        flexWrap: 'wrap',
                                                                        justifyContent: 'space-between',
                                                                        alignItems: 'center',
                                                                        alignSelf: 'center',
                                                                        width: Platform.OS === 'ios' ? 270 : 270,
                                                                        paddingTop: switchMerchant ? 10 : 10,
                                                                    }}>
                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'space-between',
                                                                            alignSelf: 'center',
                                                                            alignItems: 'center',
                                                                            width: '100%',
                                                                        }}>
                                                                        <TouchableOpacity
                                                                            onPress={() => {
                                                                                onCloseShiftBtn(1);
                                                                            }}>
                                                                            <View
                                                                                style={[
                                                                                    styles.pinBtn,
                                                                                    {
                                                                                        width: Dimensions.get('screen').width * 0.05,
                                                                                        height: Dimensions.get('screen').width * 0.05,
                                                                                    },
                                                                                ]}>
                                                                                <Text
                                                                                    style={[
                                                                                        styles.pinNo,
                                                                                        { fontSize: 0.28 * (Dimensions.get('screen').width * 0.05) },
                                                                                    ]}>
                                                                                    1
                                                                                </Text>
                                                                            </View>
                                                                        </TouchableOpacity>

                                                                        <TouchableOpacity
                                                                            onPress={() => {
                                                                                onCloseShiftBtn(2);
                                                                            }}>
                                                                            <View
                                                                                style={[
                                                                                    styles.pinBtn,
                                                                                    {
                                                                                        width: Dimensions.get('screen').width * 0.05,
                                                                                        height: Dimensions.get('screen').width * 0.05,
                                                                                    },
                                                                                ]}>
                                                                                <Text
                                                                                    style={[
                                                                                        styles.pinNo,
                                                                                        { fontSize: 0.28 * (Dimensions.get('screen').width * 0.05) },
                                                                                    ]}>
                                                                                    2
                                                                                </Text>
                                                                            </View>
                                                                        </TouchableOpacity>

                                                                        <TouchableOpacity
                                                                            onPress={() => {
                                                                                onCloseShiftBtn(3);
                                                                            }}>
                                                                            <View
                                                                                style={[
                                                                                    styles.pinBtn,
                                                                                    {
                                                                                        width: Dimensions.get('screen').width * 0.05,
                                                                                        height: Dimensions.get('screen').width * 0.05,
                                                                                    },
                                                                                ]}>
                                                                                <Text
                                                                                    style={[
                                                                                        styles.pinNo,
                                                                                        { fontSize: 0.28 * (Dimensions.get('screen').width * 0.05) },
                                                                                    ]}>
                                                                                    3
                                                                                </Text>
                                                                            </View>
                                                                        </TouchableOpacity>
                                                                    </View>
                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'space-between',
                                                                            alignSelf: 'center',
                                                                            alignItems: 'center',
                                                                            width: '100%',
                                                                        }}>
                                                                        <TouchableOpacity
                                                                            onPress={() => {
                                                                                onCloseShiftBtn(4);
                                                                            }}>
                                                                            <View
                                                                                style={[
                                                                                    styles.pinBtn,
                                                                                    {
                                                                                        width: Dimensions.get('screen').width * 0.05,
                                                                                        height: Dimensions.get('screen').width * 0.05,
                                                                                    },
                                                                                ]}>
                                                                                <Text
                                                                                    style={[
                                                                                        styles.pinNo,
                                                                                        { fontSize: 0.28 * (Dimensions.get('screen').width * 0.05) },
                                                                                    ]}>
                                                                                    4
                                                                                </Text>
                                                                            </View>
                                                                        </TouchableOpacity>

                                                                        <TouchableOpacity
                                                                            onPress={() => {
                                                                                onCloseShiftBtn(5);
                                                                            }}>
                                                                            <View
                                                                                style={[
                                                                                    styles.pinBtn,
                                                                                    {
                                                                                        width: Dimensions.get('screen').width * 0.05,
                                                                                        height: Dimensions.get('screen').width * 0.05,
                                                                                    },
                                                                                ]}>
                                                                                <Text
                                                                                    style={[
                                                                                        styles.pinNo,
                                                                                        { fontSize: 0.28 * (Dimensions.get('screen').width * 0.05) },
                                                                                    ]}>
                                                                                    5
                                                                                </Text>
                                                                            </View>
                                                                        </TouchableOpacity>

                                                                        <TouchableOpacity
                                                                            onPress={() => {
                                                                                onCloseShiftBtn(6);
                                                                            }}>
                                                                            <View
                                                                                style={[
                                                                                    styles.pinBtn,
                                                                                    {
                                                                                        width: Dimensions.get('screen').width * 0.05,
                                                                                        height: Dimensions.get('screen').width * 0.05,
                                                                                    },
                                                                                ]}>
                                                                                <Text
                                                                                    style={[
                                                                                        styles.pinNo,
                                                                                        { fontSize: 0.28 * (Dimensions.get('screen').width * 0.05) },
                                                                                    ]}>
                                                                                    6
                                                                                </Text>
                                                                            </View>
                                                                        </TouchableOpacity>
                                                                    </View>

                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'space-between',
                                                                            alignSelf: 'center',
                                                                            alignItems: 'center',
                                                                            width: '100%',
                                                                        }}>
                                                                        <TouchableOpacity
                                                                            onPress={() => {
                                                                                onCloseShiftBtn(7);
                                                                            }}>
                                                                            <View
                                                                                style={[
                                                                                    styles.pinBtn,
                                                                                    {
                                                                                        width: Dimensions.get('screen').width * 0.05,
                                                                                        height: Dimensions.get('screen').width * 0.05,
                                                                                    },
                                                                                ]}>
                                                                                <Text
                                                                                    style={[
                                                                                        styles.pinNo,
                                                                                        { fontSize: 0.28 * (Dimensions.get('screen').width * 0.05) },
                                                                                    ]}>
                                                                                    7
                                                                                </Text>
                                                                            </View>
                                                                        </TouchableOpacity>

                                                                        <TouchableOpacity
                                                                            onPress={() => {
                                                                                onCloseShiftBtn(8);
                                                                            }}>
                                                                            <View
                                                                                style={[
                                                                                    styles.pinBtn,
                                                                                    {
                                                                                        width: Dimensions.get('screen').width * 0.05,
                                                                                        height: Dimensions.get('screen').width * 0.05,
                                                                                    },
                                                                                ]}>
                                                                                <Text
                                                                                    style={[
                                                                                        styles.pinNo,
                                                                                        { fontSize: 0.28 * (Dimensions.get('screen').width * 0.05) },
                                                                                    ]}>
                                                                                    8
                                                                                </Text>
                                                                            </View>
                                                                        </TouchableOpacity>

                                                                        <TouchableOpacity
                                                                            onPress={() => {
                                                                                onCloseShiftBtn(9);
                                                                            }}>
                                                                            <View
                                                                                style={[
                                                                                    styles.pinBtn,
                                                                                    {
                                                                                        width: Dimensions.get('screen').width * 0.05,
                                                                                        height: Dimensions.get('screen').width * 0.05,
                                                                                    },
                                                                                ]}>
                                                                                <Text
                                                                                    style={[
                                                                                        styles.pinNo,
                                                                                        { fontSize: 0.28 * (Dimensions.get('screen').width * 0.05) },
                                                                                    ]}>
                                                                                    9
                                                                                </Text>
                                                                            </View>
                                                                        </TouchableOpacity>
                                                                    </View>
                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'space-between',
                                                                            alignSelf: 'center',
                                                                            alignItems: 'center',
                                                                            width: '100%',
                                                                        }}>
                                                                        <TouchableOpacity
                                                                            onPress={() => {
                                                                                onCloseShiftBtn('.');
                                                                            }}>
                                                                            <View
                                                                                style={[
                                                                                    styles.pinBtn,
                                                                                    {
                                                                                        width: Dimensions.get('screen').width * 0.05,
                                                                                        height: Dimensions.get('screen').width * 0.05,
                                                                                    },
                                                                                ]}>
                                                                                <Text
                                                                                    style={[
                                                                                        styles.pinNo,
                                                                                        { fontSize: 0.28 * (Dimensions.get('screen').width * 0.05) },
                                                                                    ]}>
                                                                                    .
                                                                                </Text>
                                                                            </View>
                                                                        </TouchableOpacity>

                                                                        <TouchableOpacity
                                                                            onPress={() => {
                                                                                onCloseShiftBtn(0);
                                                                            }}>
                                                                            <View
                                                                                style={[
                                                                                    styles.pinBtn,
                                                                                    {
                                                                                        width: Dimensions.get('screen').width * 0.05,
                                                                                        height: Dimensions.get('screen').width * 0.05,
                                                                                    },
                                                                                ]}>
                                                                                <Text
                                                                                    style={[
                                                                                        styles.pinNo,
                                                                                        { fontSize: 0.28 * (Dimensions.get('screen').width * 0.05) },
                                                                                    ]}>
                                                                                    0
                                                                                </Text>
                                                                            </View>
                                                                        </TouchableOpacity>

                                                                        <TouchableOpacity
                                                                            onPress={() => {
                                                                                onCloseShiftBtn(-1);
                                                                            }}>
                                                                            <View
                                                                                style={[
                                                                                    styles.pinBtn,
                                                                                    {
                                                                                        width: Dimensions.get('screen').width * 0.05,
                                                                                        height: Dimensions.get('screen').width * 0.05,
                                                                                    },
                                                                                ]}>
                                                                                <Icon
                                                                                    name="chevron-left"
                                                                                    size={0.525 * (Dimensions.get('screen').width * 0.05)}
                                                                                    color={'black'}
                                                                                    style={{}}
                                                                                />
                                                                            </View>
                                                                        </TouchableOpacity>
                                                                    </View>
                                                                </View>
                                                            </View>

                                                            <View>
                                                                <TouchableOpacity
                                                                    style={{
                                                                        justifyContent: 'center',
                                                                        flexDirection: 'row',
                                                                        borderWidth: 1,
                                                                        borderColor: Colors.primaryColor,
                                                                        backgroundColor: '#4E9F7D',
                                                                        borderRadius: 5,
                                                                        width: 180,
                                                                        paddingHorizontal: 10,
                                                                        height: switchMerchant ? 35 : 40,
                                                                        alignItems: 'center',
                                                                        shadowOffset: {
                                                                            width: 0,
                                                                            height: 2,
                                                                        },
                                                                        shadowOpacity: 0.22,
                                                                        shadowRadius: 3.22,
                                                                        elevation: 1,
                                                                        zIndex: -1,
                                                                    }}
                                                                    onPress={() => {
                                                                        // setState({ showModal3: true });

                                                                        setShowKeypad(false);

                                                                        setTimeout(() => {
                                                                            setShowModal3(true);
                                                                        }, 1000);

                                                                        // setOptions(true);
                                                                    }}>
                                                                    <Text
                                                                        style={{
                                                                            color: Colors.whiteColor,
                                                                            //marginLeft: 5,
                                                                            fontSize: switchMerchant ? 15 : 16,
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                        }}>
                                                                        ENTER
                                                                    </Text>
                                                                </TouchableOpacity>
                                                            </View>
                                                        </View>
                                                    </ScrollView>
                                                </View>
                                                {/* </ScrollView> */}
                                            </ModalView>

                                            {/* New UI */}
                                            <ModalView
                                                supportedOrientations={['landscape', 'portrait']}
                                                style={{
                                                    // flex: 1,
                                                    backgroundColor: Colors.primaryColor,
                                                    padding: 40,
                                                    margin: 50,
                                                    borderRadius: 12,
                                                }}
                                                animationType="fade"
                                                transparent={false}
                                                visible={showNotes}
                                                onRequestClose={() => {
                                                    Alert.alert(
                                                        'Modal is closing',
                                                        [{ text: 'OK', onPress: () => { } }],
                                                        { cancelable: false },
                                                    );
                                                }}>
                                                <View style={{
                                                    // backgroundColor: 'red',

                                                    width: windowWidth,
                                                    height: windowHeight,

                                                    ...getTransformForModalFullScreen(),
                                                }}>
                                                    <View style={{
                                                        //backgroundColor: 'transparent',
                                                        height: 50,
                                                    }}>
                                                        <TouchableOpacity
                                                            style={{
                                                                // width: '10%',
                                                                // backgroundColor: 'red',
                                                                position: 'absolute',
                                                                top: '3%',
                                                                left: '2%',
                                                            }}
                                                            //   onPress={() => { props.navigation.goBack(); }}>
                                                            onPress={() => {
                                                                // goBackButton();
                                                                setShowModalConfirm(false);
                                                                setShowNotes(false);

                                                                setShiftPayProceeding(false);
                                                                setShiftPayReason('');
                                                            }}>
                                                            <View
                                                                style={{
                                                                    paddingTop: 10,
                                                                    marginBottom: 10,
                                                                    display: 'flex',
                                                                    flexDirection: 'row',
                                                                    alignItems: 'center',
                                                                    justifyContent: 'flex-start',
                                                                }}>
                                                                <Icon
                                                                    name="chevron-left"
                                                                    size={switchMerchant ? 20 : 30}
                                                                    style={{
                                                                        color: Colors.primaryColor,
                                                                        alignSelf: 'center',
                                                                    }}
                                                                />
                                                                <Text
                                                                    style={{
                                                                        fontFamily: 'Nunitosans-Bold',
                                                                        color: Colors.primaryColor,
                                                                        fontSize: switchMerchant ? 14 : 17,
                                                                        marginBottom: Platform.OS === 'ios' ? 0 : 1,
                                                                    }}>
                                                                    Back
                                                                </Text>
                                                            </View>
                                                        </TouchableOpacity>

                                                        {/* <TouchableOpacity
                                                        style={{
                                                            // width: '10%',
                                                            // backgroundColor: 'red',
                                                            position: 'absolute',
                                                            top: '3%',
                                                            right: '2%',
                                                        }}
                                                        //   onPress={() => { props.navigation.goBack(); }}>
                                                        onPress={() => {
                                                            // goBackButton();
                                                            setShowNotes(false);

                                                            closeShift();
                                                        }}>
                                                        <View
                                                            style={{
                                                                paddingTop: 10,
                                                                marginBottom: 10,
                                                                display: 'flex',
                                                                flexDirection: 'row',
                                                                alignItems: 'center',
                                                                justifyContent: 'flex-start',
                                                            }}>
                                                            <Text
                                                                style={{
                                                                    fontFamily: 'Nunitosans-Bold',
                                                                    color: Colors.primaryColor,
                                                                    fontSize: switchMerchant ? 14 : 17,
                                                                    marginBottom: Platform.OS === 'ios' ? 0 : 1,
                                                                }}>
                                                                Skip
                                                            </Text>
                                                            <Icon
                                                                name="chevron-right"
                                                                size={switchMerchant ? 20 : 30}
                                                                style={{
                                                                    color: Colors.primaryColor,
                                                                    alignSelf: 'center',
                                                                }}
                                                            />

                                                        </View>
                                                    </TouchableOpacity> */}
                                                    </View>

                                                    <ScrollView
                                                        showsVerticalScrollIndicator={false}
                                                        nestedScrollEnabled
                                                        scrollEnabled={Dimensions.get('screen').height < 670}
                                                        style={{}}
                                                        contentContainerStyle={{
                                                            paddingBottom: windowHeight * 0.1,
                                                            backgroundColor: Colors.whiteColor,
                                                        }}
                                                    >
                                                        {/* <ScrollView horizontal={true}> */}
                                                        <View
                                                            style={[
                                                                styles.container,
                                                                {
                                                                    flexDirection: 'column',
                                                                    justifyContent: 'center',
                                                                    alignItems: 'center',
                                                                    alignContent: 'center',
                                                                },
                                                            ]}>
                                                            <View
                                                                style={{
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    paddingTop: windowHeight * 0.005
                                                                }}>
                                                                <Text
                                                                    style={{
                                                                        // fontSize: switchMerchant ? 30 : 45,
                                                                        fontSize: 0.63 * (Dimensions.get('screen').width * 0.05),
                                                                        fontWeight: 'bold',
                                                                        margin: switchMerchant ? 1 : 12,
                                                                    }}>
                                                                    {
                                                                        shiftPayProceeding
                                                                            ?
                                                                            (shiftPayType === SHIFT_PAY_TYPE.PAY_IN ? 'Pay In' : 'Pay Out')
                                                                            :
                                                                            (`${currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED
                                                                                ? 'Close'
                                                                                : 'Open'
                                                                                } Shift`)
                                                                    }
                                                                </Text>
                                                                <Text
                                                                    style={{
                                                                        // fontSize: switchMerchant ? 12 : 20,
                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                        color: Colors.fieldtTxtColor,
                                                                    }}>
                                                                    {
                                                                        shiftPayProceeding
                                                                            ?
                                                                            (shiftPayType === SHIFT_PAY_TYPE.PAY_IN ? 'Pay In Amount' : 'Pay Out Amount')
                                                                            :
                                                                            (`${currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED
                                                                                ? 'Closing'
                                                                                : 'Opening'
                                                                                } Amount`)
                                                                    }
                                                                </Text>
                                                            </View>
                                                            <View style={{ justifyContent: 'center' }}>
                                                                <Text style={{
                                                                    // fontSize: switchMerchant ? 30 : 100
                                                                    fontSize: 1.59 * (Dimensions.get('screen').width * 0.05)
                                                                }}>
                                                                    {sumOfDeno.length === 0 ? '0' : sumOfDeno.toFixed(2)}
                                                                </Text>
                                                            </View>
                                                            <ScrollView
                                                                nestedScrollEnabled
                                                                persistentScrollbar
                                                                showsVerticalScrollIndicator
                                                                style={{
                                                                    height: Platform.OS === 'ios' ? 270 : 270,
                                                                    marginBottom: 15,

                                                                    //width: '100%',
                                                                    //backgroundColor: 'blue',
                                                                }}>
                                                                <View style={{
                                                                    alignSelf: 'center',
                                                                    width: "60%",
                                                                    //backgroundColor: 'green',
                                                                    paddingTop: switchMerchant ? 10 : 10,
                                                                }}>
                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'center',
                                                                            alignSelf: 'center',
                                                                            alignItems: 'center',
                                                                            width: '100%',

                                                                            shadowColor: '#000',
                                                                            shadowOffset: {
                                                                                width: 0,
                                                                                height: 2,
                                                                            },
                                                                            shadowOpacity: 0.22,
                                                                            shadowRadius: 3.22,
                                                                            elevation: 4,
                                                                        }}>
                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    //// backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'flex-end',
                                                                                    borderRadius: 10,
                                                                                    paddingRight: 15,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                5 Cents
                                                                            </Text>
                                                                        </View>

                                                                        <TextInput
                                                                            style={[
                                                                                {
                                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.05,
                                                                                    height: Dimensions.get('screen').width * 0.03,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                    borderWidth: 1,
                                                                                    borderColor: '#E5E5E5',
                                                                                    borderWidth: 1,
                                                                                    borderColor: '#E5E5E5',

                                                                                    fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                    marginHorizontal: 10,
                                                                                    paddingHorizontal: 10,

                                                                                    // shadowColor: '#000',
                                                                                    // shadowOffset: {
                                                                                    //     width: 0,
                                                                                    //     height: 2,
                                                                                    // },
                                                                                    // shadowOpacity: 0.22,
                                                                                    // shadowRadius: 3.22,
                                                                                    // elevation: 3,
                                                                                },
                                                                            ]}
                                                                            editable={!loading}
                                                                            underlineColorAndroid={Colors.whiteColor}
                                                                            clearButtonMode="while-editing"
                                                                            placeholder="0"
                                                                            keyboardType='decimal-pad'
                                                                            onChangeText={(text) => {
                                                                                setDeno({
                                                                                    ...deno,
                                                                                    cents5: text == '' ? 0 : parseInt(text)
                                                                                });
                                                                            }}
                                                                            defaultValue={deno.cents5}
                                                                        />

                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    //// backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                = RM {((deno.cents5 == '' || isNaN(deno.cents5) ? 0 : deno.cents5) * 0.05).toFixed(2)}
                                                                            </Text>
                                                                        </View>
                                                                    </View>

                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'center',
                                                                            alignSelf: 'center',
                                                                            alignItems: 'center',
                                                                            width: '100%',

                                                                            shadowColor: '#000',
                                                                            shadowOffset: {
                                                                                width: 0,
                                                                                height: 2,
                                                                            },
                                                                            shadowOpacity: 0.22,
                                                                            shadowRadius: 3.22,
                                                                            elevation: 4,
                                                                        }}>
                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    //// backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'flex-end',
                                                                                    borderRadius: 10,
                                                                                    paddingRight: 15,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                10 Cents
                                                                            </Text>
                                                                        </View>

                                                                        <TextInput
                                                                            style={[
                                                                                {
                                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.05,
                                                                                    height: Dimensions.get('screen').width * 0.03,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                    borderWidth: 1,
                                                                                    borderColor: '#E5E5E5',
                                                                                    borderWidth: 1,
                                                                                    borderColor: '#E5E5E5',

                                                                                    fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                    marginHorizontal: 10,
                                                                                    paddingHorizontal: 10,

                                                                                    // shadowColor: '#000',
                                                                                    // shadowOffset: {
                                                                                    //     width: 0,
                                                                                    //     height: 2,
                                                                                    // },
                                                                                    // shadowOpacity: 0.22,
                                                                                    // shadowRadius: 3.22,
                                                                                    // elevation: 3,
                                                                                },
                                                                            ]}
                                                                            editable={!loading}
                                                                            underlineColorAndroid={Colors.whiteColor}
                                                                            clearButtonMode="while-editing"
                                                                            placeholder="0"
                                                                            keyboardType='decimal-pad'
                                                                            onChangeText={(text) => {
                                                                                setDeno({
                                                                                    ...deno,
                                                                                    cents10: text == '' ? 0 : parseInt(text)
                                                                                });
                                                                            }}
                                                                            defaultValue={deno.cents10}
                                                                        />

                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    //// backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                = RM {((deno.cents10 == '' || isNaN(deno.cents10) ? 0 : deno.cents10) * 0.10).toFixed(2)}
                                                                            </Text>
                                                                        </View>
                                                                    </View>

                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'center',
                                                                            alignSelf: 'center',
                                                                            alignItems: 'center',
                                                                            width: '100%',
                                                                        }}>
                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    //alignContent: 'flex-end',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'flex-end',
                                                                                    borderRadius: 10,
                                                                                    paddingRight: 15,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                20 Cents
                                                                            </Text>
                                                                        </View>

                                                                        <TextInput
                                                                            style={[
                                                                                {
                                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.05,
                                                                                    height: Dimensions.get('screen').width * 0.03,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                    borderWidth: 1,
                                                                                    borderColor: '#E5E5E5',

                                                                                    fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                    marginHorizontal: 10,
                                                                                    paddingHorizontal: 10,

                                                                                    // shadowColor: '#000',
                                                                                    // shadowOffset: {
                                                                                    //     width: 0,
                                                                                    //     height: 2,
                                                                                    // },
                                                                                    // shadowOpacity: 0.22,
                                                                                    // shadowRadius: 3.22,
                                                                                    // elevation: 3,
                                                                                },
                                                                            ]}
                                                                            editable={!loading}
                                                                            underlineColorAndroid={Colors.whiteColor}
                                                                            clearButtonMode="while-editing"
                                                                            placeholder="0"
                                                                            keyboardType='decimal-pad'
                                                                            onChangeText={(text) => {
                                                                                setDeno({
                                                                                    ...deno,
                                                                                    cents20: text == '' ? 0 : parseInt(text)
                                                                                });
                                                                            }}
                                                                            defaultValue={deno.cents20}
                                                                        />

                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                = RM {((deno.cents20 == '' || isNaN(deno.cents20) ? 0 : deno.cents20) * 0.20).toFixed(2)}
                                                                            </Text>
                                                                        </View>
                                                                    </View>

                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'center',
                                                                            alignSelf: 'center',
                                                                            alignItems: 'center',
                                                                            width: '100%',
                                                                        }}>
                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'flex-end',
                                                                                    borderRadius: 10,
                                                                                    paddingRight: 15,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                50 Cents
                                                                            </Text>
                                                                        </View>

                                                                        <TextInput
                                                                            style={[
                                                                                {
                                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.05,
                                                                                    height: Dimensions.get('screen').width * 0.03,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                    borderWidth: 1,
                                                                                    borderColor: '#E5E5E5',

                                                                                    fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                    marginHorizontal: 10,
                                                                                    paddingHorizontal: 10,

                                                                                    // shadowColor: '#000',
                                                                                    // shadowOffset: {
                                                                                    //     width: 0,
                                                                                    //     height: 2,
                                                                                    // },
                                                                                    // shadowOpacity: 0.22,
                                                                                    // shadowRadius: 3.22,
                                                                                    // elevation: 3,
                                                                                },
                                                                            ]}
                                                                            editable={!loading}
                                                                            underlineColorAndroid={Colors.whiteColor}
                                                                            clearButtonMode="while-editing"
                                                                            placeholder="0"
                                                                            keyboardType='decimal-pad'
                                                                            onChangeText={(text) => {
                                                                                setDeno({
                                                                                    ...deno,
                                                                                    cents50: text == '' ? 0 : parseInt(text)
                                                                                });
                                                                            }}
                                                                            defaultValue={deno.cents50}
                                                                        />

                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                = RM {((deno.cents50 == '' || isNaN(deno.cents50) ? 0 : deno.cents50) * 0.50).toFixed(2)}
                                                                            </Text>
                                                                        </View>

                                                                    </View>

                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'center',
                                                                            alignSelf: 'center',
                                                                            alignItems: 'center',
                                                                            width: '100%',
                                                                        }}>
                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'flex-end',
                                                                                    borderRadius: 10,
                                                                                    paddingRight: 15,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                RM1 Notes
                                                                            </Text>
                                                                        </View>

                                                                        <TextInput
                                                                            style={[
                                                                                {
                                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.05,
                                                                                    height: Dimensions.get('screen').width * 0.03,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                    borderWidth: 1,
                                                                                    borderColor: '#E5E5E5',

                                                                                    fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                    marginHorizontal: 10,
                                                                                    paddingHorizontal: 10,

                                                                                    // shadowColor: '#000',
                                                                                    // shadowOffset: {
                                                                                    //     width: 0,
                                                                                    //     height: 2,
                                                                                    // },
                                                                                    // shadowOpacity: 0.22,
                                                                                    // shadowRadius: 3.22,
                                                                                    // elevation: 3,
                                                                                },
                                                                            ]}
                                                                            editable={!loading}
                                                                            underlineColorAndroid={Colors.whiteColor}
                                                                            clearButtonMode="while-editing"
                                                                            placeholder="0"
                                                                            keyboardType='decimal-pad'
                                                                            onChangeText={(text) => {
                                                                                setDeno({
                                                                                    ...deno,
                                                                                    rm1: text == '' ? 0 : parseInt(text)
                                                                                });
                                                                            }}
                                                                            defaultValue={deno.rm1}
                                                                        />

                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                = RM {((deno.rm1 == '' || isNaN(deno.rm1) ? 0 : deno.rm1) * 1.00).toFixed(2)}
                                                                            </Text>
                                                                        </View>
                                                                    </View>

                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'center',
                                                                            alignSelf: 'center',
                                                                            alignItems: 'center',
                                                                            width: '100%',
                                                                        }}>
                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'flex-end',
                                                                                    borderRadius: 10,
                                                                                    paddingRight: 15,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                RM5 Notes
                                                                            </Text>
                                                                        </View>

                                                                        <TextInput
                                                                            style={[
                                                                                {
                                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.05,
                                                                                    height: Dimensions.get('screen').width * 0.03,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                    borderWidth: 1,
                                                                                    borderColor: '#E5E5E5',

                                                                                    fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                    marginHorizontal: 10,
                                                                                    paddingHorizontal: 10,

                                                                                    // shadowColor: '#000',
                                                                                    // shadowOffset: {
                                                                                    //     width: 0,
                                                                                    //     height: 2,
                                                                                    // },
                                                                                    // shadowOpacity: 0.22,
                                                                                    // shadowRadius: 3.22,
                                                                                    // elevation: 3,
                                                                                },
                                                                            ]}
                                                                            editable={!loading}
                                                                            underlineColorAndroid={Colors.whiteColor}
                                                                            clearButtonMode="while-editing"
                                                                            placeholder="0"
                                                                            keyboardType='decimal-pad'
                                                                            onChangeText={(text) => {
                                                                                setDeno({
                                                                                    ...deno,
                                                                                    rm5: text == '' ? 0 : parseInt(text)
                                                                                });
                                                                            }}
                                                                            defaultValue={deno.rm5}
                                                                        />

                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                = RM {((deno.rm5 == '' || isNaN(deno.rm5) ? 0 : deno.rm5) * 5.00).toFixed(2)}
                                                                            </Text>
                                                                        </View>
                                                                    </View>

                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'center',
                                                                            alignSelf: 'center',
                                                                            alignItems: 'center',
                                                                            width: '100%',
                                                                        }}>
                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'flex-end',
                                                                                    borderRadius: 10,
                                                                                    paddingRight: 15,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                RM10 Notes
                                                                            </Text>
                                                                        </View>

                                                                        <TextInput
                                                                            style={[
                                                                                {
                                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.05,
                                                                                    height: Dimensions.get('screen').width * 0.03,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                    borderWidth: 1,
                                                                                    borderColor: '#E5E5E5',

                                                                                    fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                    marginHorizontal: 10,
                                                                                    paddingHorizontal: 10,

                                                                                    // shadowColor: '#000',
                                                                                    // shadowOffset: {
                                                                                    //     width: 0,
                                                                                    //     height: 2,
                                                                                    // },
                                                                                    // shadowOpacity: 0.22,
                                                                                    // shadowRadius: 3.22,
                                                                                    // elevation: 3,
                                                                                },
                                                                            ]}
                                                                            editable={!loading}
                                                                            underlineColorAndroid={Colors.whiteColor}
                                                                            clearButtonMode="while-editing"
                                                                            placeholder="0"
                                                                            keyboardType='decimal-pad'
                                                                            onChangeText={(text) => {
                                                                                setDeno({
                                                                                    ...deno,
                                                                                    rm10: text == '' ? 0 : parseInt(text)
                                                                                });
                                                                            }}
                                                                            defaultValue={deno.rm10}
                                                                        />

                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                = RM {((deno.rm10 == '' || isNaN(deno.rm10) ? 0 : deno.rm10) * 10.00).toFixed(2)}
                                                                            </Text>
                                                                        </View>
                                                                    </View>

                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'center',
                                                                            alignSelf: 'center',
                                                                            alignItems: 'center',
                                                                            width: '100%',
                                                                        }}>
                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'flex-end',
                                                                                    borderRadius: 10,
                                                                                    paddingRight: 15,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                RM20 Notes
                                                                            </Text>
                                                                        </View>

                                                                        <TextInput
                                                                            style={[
                                                                                {
                                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.05,
                                                                                    height: Dimensions.get('screen').width * 0.03,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                    borderWidth: 1,
                                                                                    borderColor: '#E5E5E5',

                                                                                    fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                    marginHorizontal: 10,
                                                                                    paddingHorizontal: 10,

                                                                                    // shadowColor: '#000',
                                                                                    // shadowOffset: {
                                                                                    //     width: 0,
                                                                                    //     height: 2,
                                                                                    // },
                                                                                    // shadowOpacity: 0.22,
                                                                                    // shadowRadius: 3.22,
                                                                                    // elevation: 3,
                                                                                },
                                                                            ]}
                                                                            editable={!loading}
                                                                            underlineColorAndroid={Colors.whiteColor}
                                                                            clearButtonMode="while-editing"
                                                                            placeholder="0"
                                                                            keyboardType='decimal-pad'
                                                                            onChangeText={(text) => {
                                                                                setDeno({
                                                                                    ...deno,
                                                                                    rm20: text == '' ? 0 : parseInt(text)
                                                                                });
                                                                            }}
                                                                            defaultValue={deno.rm20}
                                                                        />

                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                = RM {((deno.rm20 == '' || isNaN(deno.rm20) ? 0 : deno.rm20) * 20.00).toFixed(2)}
                                                                            </Text>
                                                                        </View>
                                                                    </View>

                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'center',
                                                                            alignSelf: 'center',
                                                                            alignItems: 'center',
                                                                            width: '100%',
                                                                        }}>
                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'flex-end',
                                                                                    borderRadius: 10,
                                                                                    paddingRight: 15,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                RM50 Notes
                                                                            </Text>
                                                                        </View>

                                                                        <TextInput
                                                                            style={[
                                                                                {
                                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.05,
                                                                                    height: Dimensions.get('screen').width * 0.03,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                    borderWidth: 1,
                                                                                    borderColor: '#E5E5E5',

                                                                                    fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                    marginHorizontal: 10,
                                                                                    paddingHorizontal: 10,

                                                                                    // shadowColor: '#000',
                                                                                    // shadowOffset: {
                                                                                    //     width: 0,
                                                                                    //     height: 2,
                                                                                    // },
                                                                                    // shadowOpacity: 0.22,
                                                                                    // shadowRadius: 3.22,
                                                                                    // elevation: 3,
                                                                                },
                                                                            ]}
                                                                            editable={!loading}
                                                                            underlineColorAndroid={Colors.whiteColor}
                                                                            clearButtonMode="while-editing"
                                                                            placeholder="0"
                                                                            keyboardType='decimal-pad'
                                                                            onChangeText={(text) => {
                                                                                setDeno({
                                                                                    ...deno,
                                                                                    rm50: text == '' ? 0 : parseInt(text)
                                                                                });
                                                                            }}
                                                                            defaultValue={deno.rm50}
                                                                        />

                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                = RM {((deno.rm50 == '' || isNaN(deno.rm50) ? 0 : deno.rm50) * 50.00).toFixed(2)}
                                                                            </Text>
                                                                        </View>
                                                                    </View>

                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'center',
                                                                            alignSelf: 'center',
                                                                            alignItems: 'center',
                                                                            width: '100%',
                                                                        }}>
                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'flex-end',
                                                                                    borderRadius: 10,
                                                                                    paddingRight: 15,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                RM100 Notes
                                                                            </Text>
                                                                        </View>

                                                                        <TextInput
                                                                            style={[
                                                                                {
                                                                                    backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.05,
                                                                                    height: Dimensions.get('screen').width * 0.03,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                    borderWidth: 1,
                                                                                    borderColor: '#E5E5E5',

                                                                                    fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                    marginHorizontal: 10,
                                                                                    paddingHorizontal: 10,

                                                                                    // shadowColor: '#000',
                                                                                    // shadowOffset: {
                                                                                    //     width: 0,
                                                                                    //     height: 2,
                                                                                    // },
                                                                                    // shadowOpacity: 0.22,
                                                                                    // shadowRadius: 3.22,
                                                                                    // elevation: 3,
                                                                                },
                                                                            ]}
                                                                            editable={!loading}
                                                                            underlineColorAndroid={Colors.whiteColor}
                                                                            clearButtonMode="while-editing"
                                                                            placeholder="0"
                                                                            keyboardType='decimal-pad'
                                                                            onChangeText={(text) => {
                                                                                setDeno({
                                                                                    ...deno,
                                                                                    rm100: text == '' ? 0 : parseInt(text)
                                                                                });
                                                                            }}
                                                                            defaultValue={deno.rm100}
                                                                        />

                                                                        <View
                                                                            style={[
                                                                                {
                                                                                    // backgroundColor: Colors.fieldtBgColor,
                                                                                    // width: 70,
                                                                                    // height: 70,
                                                                                    width: Dimensions.get('screen').width * 0.1,
                                                                                    height: Dimensions.get('screen').width * 0.05,
                                                                                    marginBottom: 16,
                                                                                    alignContent: 'center',
                                                                                    justifyContent: 'center',
                                                                                    alignItems: 'center',
                                                                                    borderRadius: 10,
                                                                                },
                                                                            ]}>
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                                        fontWeight: 'bold',
                                                                                    },
                                                                                ]}>
                                                                                = RM {((deno.rm100 == '' || isNaN(deno.rm100) ? 0 : deno.rm100) * 100.00).toFixed(2)}
                                                                            </Text>
                                                                        </View>
                                                                    </View>
                                                                </View>
                                                            </ScrollView>

                                                            {/* <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                justifyContent: 'center',
                                                                alignSelf: 'center',
                                                                alignItems: 'center',
                                                                width: '100%',
                                                            }}>
                                                            <View
                                                                style={[
                                                                    {
                                                                        backgroundColor: Colors.fieldtBgColor,
                                                                        // width: 70,
                                                                        // height: 70,
                                                                        width: Dimensions.get('screen').width * 0.09,
                                                                        height: Dimensions.get('screen').width * 0.03,
                                                                        marginBottom: 16,
                                                                        alignContent: 'center',
                                                                        justifyContent: 'center',
                                                                        alignItems: 'center',
                                                                        borderRadius: 10,
                                                                    },
                                                                ]}>
                                                                <Text
                                                                    style={[
                                                                        {
                                                                            fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
                                                                            fontWeight: 'bold',
                                                                        },
                                                                    ]}>
                                                                    {sumOfDeno.toFixed(2)}
                                                                </Text>
                                                            </View>
                                                        </View> */}

                                                            <View>
                                                                <TouchableOpacity
                                                                    style={{
                                                                        justifyContent: 'center',
                                                                        flexDirection: 'row',
                                                                        borderWidth: 1,
                                                                        borderColor: Colors.primaryColor,
                                                                        backgroundColor: '#4E9F7D',
                                                                        borderRadius: 5,
                                                                        width: 180,
                                                                        paddingHorizontal: 10,
                                                                        height: switchMerchant ? 35 : 40,
                                                                        alignItems: 'center',
                                                                        shadowOffset: {
                                                                            width: 0,
                                                                            height: 2,
                                                                        },
                                                                        shadowOpacity: 0.22,
                                                                        shadowRadius: 3.22,
                                                                        elevation: 1,
                                                                        zIndex: -1,
                                                                    }}
                                                                    onPress={() => {
                                                                        setClosingAmount(sumOfDeno.toString());

                                                                        setTimeout(() => {
                                                                            setShowModal3(true);
                                                                        }, 1000);

                                                                        setShowNotes(false);
                                                                        // setOptions(true);
                                                                    }}>
                                                                    <Text
                                                                        style={{
                                                                            color: Colors.whiteColor,
                                                                            //marginLeft: 5,
                                                                            fontSize: switchMerchant ? 15 : 16,
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                        }}>
                                                                        ENTER
                                                                    </Text>
                                                                </TouchableOpacity>
                                                            </View>
                                                        </View>
                                                    </ScrollView>
                                                </View>
                                                {/* </ScrollView> */}
                                            </ModalView>

                                            <ModalView
                                                supportedOrientations={['landscape', 'portrait']}
                                                style={{
                                                    backgroundColor: Colors.primaryColor,
                                                    padding: 20,
                                                    margin: 30,
                                                }}
                                                animationType="fade"
                                                transparent
                                                visible={showModal3}
                                                onRequestClose={() => {
                                                    Alert.alert(
                                                        'Modal has been closed',
                                                        [{ text: 'OK', onPress: () => { } }],
                                                        { cancelable: false },
                                                    );
                                                }}>
                                                <View
                                                    style={{
                                                        backgroundColor: 'rgba(0,0,0,0.5)',
                                                        flex: 1,
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                    }}>
                                                    <View
                                                        style={{
                                                            width: 350,
                                                            height: 270,
                                                            backgroundColor: 'white',
                                                            borderRadius: 12,
                                                            alignItems: 'center',
                                                            justifyContent: 'space-around',

                                                            ...getTransformForModalInsideNavigation(),
                                                        }}>
                                                        <View
                                                            style={{
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                width: windowWidth / 2,
                                                            }}>
                                                            <Text
                                                                style={{
                                                                    fontWeight: 'bold',
                                                                    fontSize: switchMerchant ? 16 : 20,
                                                                    marginBottom: 20,
                                                                }}>
                                                                Confirm
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    color: Colors.fieldtTxtColor,
                                                                    fontSize: 16,
                                                                    marginBottom: 10,
                                                                }}>
                                                                Are you sure the amount is
                                                            </Text>
                                                            <Text style={{ fontWeight: 'bold', fontSize: 16, width: windowWidth / 2, textAlign: 'center' }}>
                                                                RM {parseFloat(closingAmount.length > 0 ? closingAmount : '0').toFixed(2)}
                                                            </Text>
                                                        </View>
                                                        <View
                                                            style={{
                                                                alignItems: 'center',
                                                                flexDirection: 'row',
                                                            }}>
                                                            <TouchableOpacity
                                                                onPress={() => {
                                                                    // setState({
                                                                    //   showModal3: false,
                                                                    //   options: true,
                                                                    //   showKeypad: true,
                                                                    // }),
                                                                    // closingShift();

                                                                    if (!isNaN(closingAmount)) {
                                                                        setShowModal3(false);
                                                                        // setOptions(true);
                                                                        setShowKeypad(false);

                                                                        if (shiftPayProceeding) {
                                                                            updateOutletShiftPayInOut();
                                                                            //setShowKeypad(false);
                                                                        }
                                                                        else if (
                                                                            currOutletShiftStatus ===
                                                                            OUTLET_SHIFT_STATUS.OPENED
                                                                        ) {
                                                                            closeShift();
                                                                        } else {
                                                                            openShift();
                                                                        }
                                                                    } else {
                                                                        Alert.alert('Info', 'Invalid amount');
                                                                    }
                                                                }}>
                                                                <View
                                                                    style={{
                                                                        backgroundColor: !options
                                                                            ? Colors.whiteColor
                                                                            : Colors.primaryColor,
                                                                        justifyContent: 'center',
                                                                        flexDirection: 'row',
                                                                        borderWidth: 1,
                                                                        borderColor: !options
                                                                            ? Colors.whiteColor
                                                                            : Colors.primaryColor,
                                                                        borderRadius: 5,
                                                                        width: 100,
                                                                        paddingHorizontal: 10,
                                                                        height: 40,
                                                                        alignItems: 'center',
                                                                        shadowOffset: {
                                                                            width: 0,
                                                                            height: 2,
                                                                        },
                                                                        shadowOpacity: 0.22,
                                                                        shadowRadius: 3.22,
                                                                        elevation: 1,
                                                                        zIndex: -1,
                                                                        marginRight: 15,
                                                                    }}>
                                                                    <Text
                                                                        style={{
                                                                            color: !options
                                                                                ? Colors.fieldtTxtColor
                                                                                : Colors.whiteColor,
                                                                            fontSize: switchMerchant ? 15 : 16,
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                        }}>
                                                                        YES
                                                                    </Text>
                                                                </View>
                                                            </TouchableOpacity>
                                                            <TouchableOpacity
                                                                onPress={() => {
                                                                    // setState({
                                                                    //   showModal3: false,
                                                                    //   options: false,
                                                                    // });

                                                                    setDeno({
                                                                        cents5: 0,
                                                                        cents10: 0,
                                                                        cents20: 0,
                                                                        cents50: 0,
                                                                        rm1: 0,
                                                                        rm5: 0,
                                                                        rm10: 0,
                                                                        rm20: 0,
                                                                        rm50: 0,
                                                                        rm100: 0,
                                                                    });

                                                                    setClosingAmount('');
                                                                    setShowModal3(false);
                                                                    setShowNotes(true);
                                                                    // setOptions(false);
                                                                }}>
                                                                <View
                                                                    style={{
                                                                        backgroundColor: !options
                                                                            ? Colors.primaryColor
                                                                            : Colors.fieldtBgColor,
                                                                        justifyContent: 'center',
                                                                        flexDirection: 'row',
                                                                        borderWidth: 1,
                                                                        borderColor: !options
                                                                            ? Colors.primaryColor
                                                                            : Colors.fieldtBgColor,
                                                                        borderRadius: 5,
                                                                        width: 100,
                                                                        paddingHorizontal: 10,
                                                                        height: 40,
                                                                        alignItems: 'center',
                                                                        shadowOffset: {
                                                                            width: 0,
                                                                            height: 2,
                                                                        },
                                                                        shadowOpacity: 0.22,
                                                                        shadowRadius: 3.22,
                                                                        elevation: 1,
                                                                        zIndex: -1,
                                                                    }}>
                                                                    <Text
                                                                        style={{
                                                                            color: options
                                                                                ? Colors.fieldtTxtColor
                                                                                : Colors.primaryColor,
                                                                            fontSize: switchMerchant ? 15 : 16,
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                        }}>
                                                                        NO
                                                                    </Text>
                                                                </View>
                                                            </TouchableOpacity>
                                                        </View>
                                                    </View>
                                                </View>
                                            </ModalView>
                                        </View>
                                }
                            </View>
                        </View>
                    </ScrollView>
                </ScrollView>
            </View>
        </UserIdleWrapper>)
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        fontFamily: 'NunitoSans-Regular',
    },
    iosStyle: {
        paddingHorizontal: Platform.OS !== 'ios' ? 0 : 30,
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    headerLogo1: {
        width: '100%',
        height: '100%',
    },
    list: {
        paddingVertical: 20,
        paddingHorizontal: 30,
        flexDirection: 'row',
        alignItems: 'center',
    },
    listItem: {
        fontFamily: 'NunitoSans-Regular',
        marginLeft: 20,
        color: Colors.descriptionColor,
        fontSize: 16,
    },
    sidebar: {
        width: Dimensions.get('screen').width * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //     width: 0,
        //     height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    content: {
        padding: 30,
        width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
        // backgroundColor: 'lightgrey',
        backgroundColor: Colors.highlightColor,
        height: Dimensions.get('screen').height * 1,
    },
    textInput: {
        fontFamily: 'NunitoSans-Regular',
        width: 300,
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        marginRight: 0,
        flex: 1,
        flexDirection: 'row',
    },
    textInputLocation: {
        fontFamily: 'NunitoSans-Regular',
        width: 300,
        height: 100,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        marginRight: 10,
    },
    textInput8: {
        fontFamily: 'NunitoSans-Regular',
        width: 75,
        height: 50,
        flex: 1,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
    },
    textInput9: {
        fontFamily: 'NunitoSans-Regular',
        width: 110,
        height: Platform.OS == 'ios' ? 30 : 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        flexDirection: 'row',
        justifyContent: 'center',
    },
    textInput10: {
        fontFamily: 'NunitoSans-Regular',
        width: 200,
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        flexDirection: 'row',
        justifyContent: 'center',
    },
    textInput1: {
        fontFamily: 'NunitoSans-Regular',
        width: 250,
        height: 40,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginBottom: 10,
    },
    textInput2: {
        fontFamily: 'NunitoSans-Regular',
        width: 300,
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginRight: 30,
    },
    textInput3: {
        fontFamily: 'NunitoSans-Regular',
        width: '85%',
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        marginTop: 10,
        marginBottom: 10,
        borderRadius: 10,
        alignSelf: 'center',
        paddingHorizontal: 10,
    },
    textInput4: {
        width: '85%',
        height: 70,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        marginTop: 10,
        marginBottom: 10,
        borderRadius: 10,
    },
    textInput5: {
        fontFamily: 'NunitoSans-Regular',
        width: 300,
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 50,
    },
    textInput6: {
        fontFamily: 'NunitoSans-Regular',
        width: '80 %',
        padding: 16,
        backgroundColor: Colors.fieldtBgColor,
        marginRight: 30,
        borderRadius: 10,
        alignSelf: 'center',
    },
    textInput7: {
        fontFamily: 'NunitoSans-Regular',
        width: 300,
        height: 80,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 7,
        marginRight: 30,
    },
    button: {
        backgroundColor: Colors.primaryColor,
        width: 150,
        padding: 8,
        borderRadius: 10,
        alignItems: 'center',
        alignSelf: 'center',
        marginBottom: 20,
    },
    button1: {
        width: '15%',
        padding: 8,
        borderRadius: 10,
        alignItems: 'center',
        alignSelf: 'center',
        marginBottom: 20,
    },
    button2: {
        backgroundColor: Colors.primaryColor,
        width: '60%',
        padding: 8,
        borderRadius: 10,
        alignItems: 'center',
        marginLeft: '2%',
    },
    button3: {
        backgroundColor: Colors.primaryColor,
        width: '30%',
        height: 50,
        borderRadius: 10,
        alignItems: 'center',
        alignSelf: 'center',
        marginBottom: 30,
    },
    textSize: {
        fontSize: 19,
        fontFamily: 'NunitoSans-SemiBold',
    },
    viewContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        flex: 0,
        width: '100%',
        marginBottom: 15,
    },
    openHourContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        flex: 1,
        marginBottom: 15,
        width: '100%',
    },
    addButtonView: {
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        width: 200,
        height: 40,
        alignItems: 'center',
    },
    addButtonView1: {
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        width: 150,
        height: 40,
        alignItems: 'center',
    },
    addNewView: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        marginBottom: 65,
        marginTop: 7,
        width: '83%',
        alignSelf: 'flex-end',
    },
    addNewView1: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginBottom: 10,
        alignItems: 'center',
    },
    merchantDisplayView: {
        flexDirection: 'row',
        flex: 1,
        marginLeft: '17%',
    },
    shiftView: {
        flexDirection: 'row',
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 50,
        // width: 200,
        // height: 60,
        paddingHorizontal: 15,
        paddingVertical: 10,
        alignItems: 'center',
        marginTop: 30,
        alignSelf: 'center',
        justifyContent: 'center',
    },
    shiftText: {
        // marginLeft: '15%',
        color: Colors.primaryColor,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 25,
    },
    closeView: {
        flexDirection: 'row',
        borderRadius: 5,
        borderColor: Colors.primaryColor,
        borderWidth: 1,
        width: 200,
        height: 40,
        alignItems: 'center',
        marginTop: 30,
        alignSelf: 'center',
    },
    taxView: {
        flexDirection: 'row',
        borderWidth: 2,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        width: 150,
        height: 40,
        alignItems: 'center',
        marginTop: 20,
        alignSelf: 'flex-end',
    },
    sectionView: {
        flexDirection: 'row',
        borderRadius: 5,
        padding: 16,
        alignItems: 'center',
    },
    receiptView: {
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        width: 200,
        height: 40,
        alignItems: 'center',
        alignSelf: 'flex-end',
    },
    pinBtn: {
        backgroundColor: Colors.fieldtBgColor,
        // width: 70,
        // height: 70,
        width: Dimensions.get('screen').width * 0.05,
        height: Dimensions.get('screen').width * 0.05,
        marginBottom: 16,
        alignContent: 'center',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 10,
    },
    pinNo: {
        // fontSize: 20,
        fontSize: 0.28 * (Dimensions.get('screen').width * 0.05),
        fontWeight: 'bold',
    },
    confirmBox: {
        width: '30%',
        height: '30%',
        borderRadius: 30,
        backgroundColor: Colors.whiteColor,
    },
    logoTxt: {
        color: Colors.descriptionColor,
        fontSize: 20,
        letterSpacing: 7,
        marginTop: 10,
        alignSelf: 'center',
        marginBottom: 40,
    },
    headerLeftStyle: {
        width: Dimensions.get('screen').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
export default SettingScreen;
