import { Text } from "react-native-fast-text";
import React, { Component, useEffect, useState } from 'react';
import {
  StyleSheet,
  Image,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
  Modal as ModalComponent,
  KeyboardAvoidingView,
  Dimensions,
  Platform,
  ActivityIndicator,
  useWindowDimensions,
  DevSettings,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import * as Token from '../util/Token';
import * as User from '../util/User';
import auth from '@react-native-firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Switch from 'react-native-switch-pro';
import { CommonStore } from '../store/commonStore';
import {
  getTransformForScreenOutsideNavigation,
  isTablet
} from '../util/common';
import { UserStore } from '../store/userStore';
import { listenToFirestoreChanges } from '../util/common';
import { MerchantStore } from '../store/merchantStore';
import NetInfo from "@react-native-community/netinfo";
import { APP_TYPE, ROLE_TYPE } from '../constant/common';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useKeyboard } from '../hooks';
import { Collections } from '../constant/firebase';
import firestore from '@react-native-firebase/firestore';
import Entypo from 'react-native-vector-icons/Entypo';
import moment from 'moment';
import { isTablet as isTabletOriginal } from 'react-native-device-detection';
// import { storageMMKV } from '../util/storageMMKV';

const axios = require('axios');
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const LoginScreen = props => {
  const {
    checkLogin,
    switchLogin,
    changeToPinLogin,
  } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [keyboardHeight] = useKeyboard();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [reset, setReset] = useState(false);
  const [login, setLogin] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingModal, setLoadingModal] = useState(false);

  /////////////////////////////////////////////////

  // 2022-08-09 - Added support code login (staff)

  const [showSupportCodeLogin, setShowSupportCodeLogin] = useState(false);
  const [supportCode, setSupportCode] = useState('');

  /////////////////////////////////////////////////

  const switchMerchant = CommonStore.useState(s => s.switchMerchant);
  const isAuthenticating = CommonStore.useState(s => s.isAuthenticating);

  const simulateTabletMode = CommonStore.useState(s => s.simulateTabletMode);

  // function here

  console.log(`LoginScreen, windowWidth: ${windowWidth}`);
  console.log(`LoginScreen, windowHeight: ${windowHeight}`);

  useEffect(() => {
    // console.log('isAuthenticating');
    // console.log(isAuthenticating);

    CommonStore.update(s => {
      s.isAuthenticating = false;
    });
  }, []);


  useEffect(() => {
    const checkTabletMode = async () => {
      const isTabletResult = await isTablet();

      if (!isTabletOriginal && isTabletResult) {
        var valueToSet = isTabletResult;

        await global.watermelonDBDatabase.localStorage.set('isTabletForce', valueToSet ? '0' : '1');
        global.isTabletForce = valueToSet ? '0' : '1';

        CommonStore.update(s => {
          s.switchMerchant = false;
        });

        Alert.alert('Info', 'Please close the app and open again for the changes to take effect.');
      }
    };

    checkTabletMode();
  }, []);

  const goReset = () => {
    const body = {
      email,
    };
    ApiClient.POST(API.userResetPassword, body).then((result) => {

      if (result == true) {
        Alert.alert(
          'Success',
          'Temporary password reset link has been sent to your email',
          [
            {
              text: 'OK',
              onPress: () => {
                setReset(!reset);
              },
            },
          ],
          { cancelable: false },
        );
      }
      else {
        Alert.alert(
          'Error',
          'Unable to send the reset password email',
          [
            {
              text: 'OK',
              onPress: () => {
                setReset(!reset);
              },
            },
          ],
          { cancelable: false },
        );
      }
    });
  };

  const goLogin = async () => {
    if (!showSupportCodeLogin) {
      if (!email || !password) {
        Alert.alert('Login failed', "Empty email and/or password", [
          {
            text: "OK", onPress: () => {
              setLoadingModal(false)
            }
          }
        ],
          { cancelable: false });

        return;
      }

      NetInfo.fetch().then(async state => {
        // console.log("Connection type", state.type);
        // console.log("Is connected?", state.isInternetReachable);
        // console.log(state);

        if (state.isInternetReachable) {
          try {
            let res = {};
            try {
              res = await auth().signInWithEmailAndPassword(
                email,
                password
              );
            }
            catch (error) {
              // console.log('existing email and password not found');
              console.error(error);

              setLoadingModal(false);

              // Alert.alert('Login failed', "Invalid email and password", [
              //   { text: "OK", onPress: () => { } }
              // ],
              //   { cancelable: false });

              // CommonStore.update(s => {
              //   s.isAuthenticating = false;
              // });
            }

            if (res.user === undefined) {
              // fail to find merchant in firebase, try to login using legacy user id/password

              ApiClient.POST(API.legacyLogin, {
                username: email,
                password,
              }).then(async (result) => {
                if (result && result.idToken) {
                  let firebaseToken = result.idToken;

                  try {
                    res = await auth().signInWithCustomToken(result.customToken);
                  }
                  catch (error) {
                    // console.log('failed to login with custom token');
                  }

                  await AsyncStorage.setItem('customToken', result.customToken ? result.customToken : '');
                  await AsyncStorage.setItem('firebaseToken', firebaseToken ? firebaseToken : '');

                  // ApiClient.GET(API.getToken + firebaseToken).then(async (result) => {
                  ApiClient.GET(`${API.getToken + firebaseToken}&app=${APP_TYPE.MERCHANT}`).then(async (result) => {
                    // setLoading(false);                  

                    if (result && result.merchantId) {
                      if (result && result.token) {
                        Token.setToken(result.token);
                        Token.setRefreshToken(result.refreshToken);

                        await AsyncStorage.setItem('accessToken', result.token ? result.token : '');

                        ApiClient.GET(API.userAdmin).then(async (userData) => {
                          CommonStore.update(s => {
                            s.supportCodeData = null;
                          });

                          await AsyncStorage.setItem('supportCodeData', '');

                          global.supportCodeData = null;

                          User.setUserData(userData);
                          User.setName(userData.name);
                          User.setRefreshToken(userData.refreshToken);
                          User.setUserId(userData.firebaseUid);
                          User.setlogin(true);
                          User.setMerchantId(userData.merchantId);
                          User.setOutletId(userData.outletId);

                          if (userData.role === ROLE_TYPE.ADMIN) {
                            await AsyncStorage.setItem(
                              'email',
                              email ? email : ''
                            );
                            await AsyncStorage.setItem(
                              'password',
                              password ? password : ''
                            );

                            await AsyncStorage.setItem('last.accessToken', result.token ? result.token : '');

                            await AsyncStorage.setItem(
                              'last.userData',
                              userData ? JSON.stringify(userData) : JSON.stringify({})
                            );

                            await AsyncStorage.setItem(
                              'last.refreshToken',
                              userData.refreshToken ? userData.refreshToken : ''
                            );
                          }

                          await AsyncStorage.setItem(
                            'loggedIn',
                            "true"
                          );

                          await AsyncStorage.setItem(
                            'userData',
                            userData ? JSON.stringify(userData) : JSON.stringify({})
                          );

                          ////////////////////////////////////

                          await AsyncStorage.setItem(
                            'merchantId',
                            userData.merchantId ? userData.merchantId : ''
                          );

                          await AsyncStorage.setItem(
                            'refreshToken',
                            userData.refreshToken ? userData.refreshToken : ''
                          );

                          await AsyncStorage.setItem(
                            'role',
                            userData.role ? userData.role : ''
                          );

                          await AsyncStorage.setItem(
                            'firebaseUid',
                            userData.firebaseUid ? userData.firebaseUid : ''
                          );

                          if (userData.isAlphaUser) {
                            await AsyncStorage.setItem(
                              'isAlphaUser',
                              '1'
                            );
                          }
                          else {
                            await AsyncStorage.setItem(
                              'isAlphaUser',
                              '0'
                            );
                          }

                          if (userData.isBetaUser) {
                            await AsyncStorage.setItem(
                              'isBetaUser',
                              '1'
                            );
                          }
                          else {
                            await AsyncStorage.setItem(
                              'isBetaUser',
                              '0'
                            );
                          }

                          await AsyncStorage.setItem(
                            'privileges',
                            JSON.stringify(userData.privileges ? userData.privileges : []),
                          );

                          await AsyncStorage.setItem(
                            'screensToBlock',
                            JSON.stringify(userData.screensToBlock ? userData.screensToBlock : [])
                          );

                          await AsyncStorage.setItem(
                            'currOutletId',
                            userData.outletId ? userData.outletId : '',
                          );

                          await AsyncStorage.setItem(
                            'isMasterAccount',
                            userData.isMasterAccount !== undefined ?
                              (userData.isMasterAccount ? '1' : '0') :
                              '1',
                          );

                          global.currUserRole = userData.role;

                          UserStore.update(s => {
                            s.firebaseUid = userData.firebaseUid;
                            s.merchantId = userData.merchantId;

                            s.role = userData.role;

                            s.isAlphaUser = userData.isAlphaUser ? true : false;

                            s.isBetaUser = userData.isBetaUser ? true : false;

                            // Email login no need first
                            // s.privileges = userData.privileges;
                          });

                          MerchantStore.update(s => {
                            s.currOutletId = userData.outletId;
                          });

                          ////////////////////////////////////

                          // ApiClient.GET(API.outlet2 + User.getOutletId()).then((result) => {
                          //   User.setName(result.name);
                          //   User.setQueueStatus(result.queueStatus);
                          //   User.setRefreshCurrentAction(result.reservationStatus);
                          //   checkLogin(true)
                          // });

                          CommonStore.update(s => {
                            s.isAuthenticating = false;
                          });

                          // checkLogin(true);                        

                          // clock in 
                          // let dateTime = Date.now();

                          // let body = {
                          //   employeeId: userData.firebaseUid,
                          //   loginTime: dateTime,

                          //   merchantId: userData.merchantId,
                          //   outletId: userData.outletId,
                          // }

                          // ApiClient.POST(API.updateUserClockInOut, body).then((result) => {
                          //   // console.log('updateUserClockIn', result);
                          // });

                          switchLogin();
                        });
                      }
                      else {
                        Alert.alert('Login failed', "Invalid merchant account", [
                          { text: "OK", onPress: () => { setLoadingModal(false) } }
                        ],
                          { cancelable: false });

                        CommonStore.update(s => {
                          s.isAuthenticating = false;
                        });
                      }
                    } else {
                      Alert.alert('Login failed', "Invalid merchant account", [
                        { text: "OK", onPress: () => { setLoadingModal(false) } }
                      ],
                        { cancelable: false });

                      CommonStore.update(s => {
                        s.isAuthenticating = false;
                      });
                    }
                  });
                }

                if (result && result.status === 'error') {
                  Alert.alert('Error', "Invalid email and/or password", [
                    { text: "OK", onPress: () => { setLoadingModal(false) } }
                  ],
                    { cancelable: false });

                  CommonStore.update(s => {
                    s.isAuthenticating = false;
                  });
                }
              }).catch(ex => {
                console.error('legacyLogin error');
                console.error(ex);

                Alert.alert('Login failed', "Invalid merchant account", [
                  { text: "OK", onPress: () => { setLoadingModal(false) } }
                ],
                  { cancelable: false });

                CommonStore.update(s => {
                  s.isAuthenticating = false;
                });
              });
            }
            else {
              // means firebase email & password auth success

              let { user } = res;
              let firebaseToken = await user.getIdToken();
              ApiClient.GET(`${API.getToken + firebaseToken}&app=${APP_TYPE.MERCHANT}`).then(async (result) => {
                // setLoading(false);              

                if (result && result.merchantId) {
                  if (result && result.token) {
                    Token.setToken(result.token);
                    Token.setRefreshToken(result.refreshToken);

                    await AsyncStorage.setItem('accessToken', result.token ? result.token : '');

                    ApiClient.GET(API.userAdmin).then(async (userData) => {
                      CommonStore.update(s => {
                        s.supportCodeData = null;
                      });

                      await AsyncStorage.setItem('supportCodeData', '');

                      global.supportCodeData = null;

                      User.setUserData(userData);
                      User.setName(userData.name);
                      User.setRefreshToken(userData.refreshToken);
                      User.setUserId(userData.firebaseUid);
                      User.setlogin(true);
                      User.setMerchantId(userData.merchantId);
                      User.setOutletId(userData.outletId);

                      if (userData.role === ROLE_TYPE.ADMIN) {
                        await AsyncStorage.setItem(
                          'email',
                          email ? email : ''
                        );
                        await AsyncStorage.setItem(
                          'password',
                          password ? password : ''
                        );

                        await AsyncStorage.setItem('last.accessToken', result.token ? result.token : '');

                        await AsyncStorage.setItem(
                          'last.userData',
                          userData ? JSON.stringify(userData) : JSON.stringify({})
                        );

                        await AsyncStorage.setItem(
                          'last.refreshToken',
                          userData.refreshToken ? userData.refreshToken : ''
                        );
                      }

                      await AsyncStorage.setItem(
                        'loggedIn',
                        "true"
                      );

                      await AsyncStorage.setItem(
                        'userData',
                        userData ? JSON.stringify(userData) : JSON.stringify({})
                      );

                      ////////////////////////////////////

                      await AsyncStorage.setItem(
                        'merchantId',
                        userData.merchantId ? userData.merchantId : ''
                      );

                      await AsyncStorage.setItem(
                        'refreshToken',
                        userData.refreshToken ? userData.refreshToken : ''
                      );

                      await AsyncStorage.setItem(
                        'role',
                        userData.role ? userData.role : ''
                      );

                      await AsyncStorage.setItem(
                        'firebaseUid',
                        userData.firebaseUid ? userData.firebaseUid : ''
                      );

                      if (userData.isAlphaUser) {
                        await AsyncStorage.setItem(
                          'isAlphaUser',
                          '1'
                        );
                      }
                      else {
                        await AsyncStorage.setItem(
                          'isAlphaUser',
                          '0'
                        );
                      }

                      if (userData.isBetaUser) {
                        await AsyncStorage.setItem(
                          'isBetaUser',
                          '1'
                        );
                      }
                      else {
                        await AsyncStorage.setItem(
                          'isBetaUser',
                          '0'
                        );
                      }

                      await AsyncStorage.setItem(
                        'privileges',
                        JSON.stringify(userData.privileges ? userData.privileges : [])
                      );

                      await AsyncStorage.setItem(
                        'screensToBlock',
                        JSON.stringify(userData.screensToBlock ? userData.screensToBlock : [])
                      );

                      await AsyncStorage.setItem(
                        'currOutletId',
                        userData.outletId ? userData.outletId : '',
                      );

                      await AsyncStorage.setItem(
                        'isMasterAccount',
                        userData.isMasterAccount !== undefined ?
                          (userData.isMasterAccount ? '1' : '0') :
                          '1',
                      );

                      global.currUserRole = userData.role;

                      UserStore.update(s => {
                        s.firebaseUid = userData.firebaseUid;
                        s.merchantId = userData.merchantId;

                        s.role = userData.role;

                        s.isAlphaUser = userData.isAlphaUser ? true : false;

                        s.isBetaUser = userData.isBetaUser ? true : false;

                        // Email login no need first
                        // s.privileges = userData.privileges;
                      });

                      MerchantStore.update(s => {
                        s.currOutletId = userData.outletId;
                      });

                      ////////////////////////////////////

                      // ApiClient.GET(API.outlet2 + User.getOutletId()).then((result) => {
                      //   User.setName(result.name);
                      //   User.setQueueStatus(result.queueStatus);
                      //   User.setRefreshCurrentAction(result.reservationStatus);
                      //   checkLogin(true)
                      // });

                      CommonStore.update(s => {
                        s.isAuthenticating = false;
                      });

                      // checkLogin(true);                    

                      // clock in 
                      // let dateTime = Date.now();

                      // let body = {
                      //   employeeId: userData.firebaseUid,
                      //   loginTime: dateTime,

                      //   merchantId: userData.merchantId,
                      //   outletId: userData.outletId,
                      // }

                      // ApiClient.POST(API.updateUserClockInOut, body).then((result) => {
                      //   // console.log('updateUserClockIn', result);
                      // });

                      switchLogin();
                    });
                  }
                  else {
                    Alert.alert('Login failed', "Invalid merchant account", [
                      { text: "OK", onPress: () => { setLoadingModal(false) } }
                    ],
                      { cancelable: false });

                    CommonStore.update(s => {
                      s.isAuthenticating = false;
                    });
                  }
                } else {
                  Alert.alert('Login failed', "Invalid merchant account", [
                    { text: "OK", onPress: () => { setLoadingModal(false) } }
                  ],
                    { cancelable: false });

                  CommonStore.update(s => {
                    s.isAuthenticating = false;
                  });
                }
              });
            }
          } catch (error) {
            Alert.alert('Login failed', "Invalid email and/or password", [
              { text: "OK", onPress: () => { setLoadingModal(false) } }
            ],
              { cancelable: false });
            // setState({ loading: false }); test herks

            CommonStore.update(s => {
              s.isAuthenticating = false;
            });
          }
        }
        else {
          // 2022-07-04 changes, disable first

          // offline mode

          // if user login once, and have @userStore and @merchantStore cached locally, can just log them in

          // const userStoreRaw = await AsyncStorage.getItem('@userStore');
          // const merchantStoreRaw = await AsyncStorage.getItem('@merchantStore');
          // const userStoreRaw = storageMMKV.getString('@userStore');
          // const merchantStoreRaw = storageMMKV.getString('@merchantStore');
          const userStoreRaw = await global.watermelonDBDatabase.localStorage.get('@userStore');
          const merchantStoreRaw = await global.watermelonDBDatabase.localStorage.get('@merchantStore');
          const lastAccessToken = await AsyncStorage.getItem('last.accessToken');
          const lastUserData = await AsyncStorage.getItem('last.userData');
          const lastRefreshToken = await AsyncStorage.getItem('last.refreshToken');

          // console.log('check store');
          // console.log(userStoreRaw);
          // console.log(merchantStoreRaw);
          // console.log(lastAccessToken);
          // console.log(lastUserData);
          // console.log(lastRefreshToken);

          if (userStoreRaw && merchantStoreRaw && lastAccessToken && lastUserData && lastRefreshToken) {
            // console.log('last login - pass');

            const storedEmail = await AsyncStorage.getItem('email');
            const storedPassword = await AsyncStorage.getItem('password');

            if (email === storedEmail && password === storedPassword) {
              // console.log('same credentials - pass');

              const userStoreData = JSON.parse(userStoreRaw);
              const merchantStoreData = JSON.parse(merchantStoreRaw);

              UserStore.replace(userStoreData);
              MerchantStore.replace(merchantStoreData);

              await AsyncStorage.setItem('accessToken', lastAccessToken ? lastAccessToken : '');
              await AsyncStorage.setItem('userData', lastUserData ? lastUserData : '');
              await AsyncStorage.setItem('refreshToken', lastRefreshToken ? lastRefreshToken : '');

              const userData = JSON.parse(lastUserData);

              Token.setToken(lastAccessToken);
              Token.setRefreshToken(lastRefreshToken);

              User.setUserData(userData);
              User.setName(userData.name);
              User.setRefreshToken(userData.refreshToken);
              User.setUserId(userData.firebaseUid);
              User.setlogin(true);
              User.setMerchantId(userData.merchantId);
              User.setOutletId(userData.outletId);

              global.isOfflineAccess = true;

              checkLogin(true);
            }
            else {
              Alert.alert('Info', 'Your device is currently offline, please sign in with the last used account');
              setLoadingModal(false);
            }
          }
          else {
            Alert.alert('Info', 'Connect to the internet and sign in to enable offline mode');
            setLoadingModal(false);
          }

          CommonStore.update(s => {
            s.isAuthenticating = false;
          });
        }
      });
    }
    else {
      // support code login

      if (
        !supportCode ||
        supportCode.length !== 12 ||
        !(new RegExp('[A-Z0-9]+$', 'g').test(supportCode))
      ) {
        Alert.alert('Login failed', "Invalid or empty support code.", [
          {
            text: "OK", onPress: () => {
              setLoadingModal(false);
            }
          }
        ],
          { cancelable: false });

        return;
      }

      NetInfo.fetch().then(async state => {
        // console.log("Connection type", state.type);
        // console.log("Is connected?", state.isInternetReachable);
        // console.log(state);

        if (state.isInternetReachable) {
          try {
            let res = {};
            try {
              res = await auth().signInAnonymously(
                // email,
                // password
              );
            }
            catch (error) {
              // console.log('existing email and password not found');

              setLoadingModal(false);

              // Alert.alert('Login failed', "Invalid email and password", [
              //   { text: "OK", onPress: () => { } }
              // ],
              //   { cancelable: false });

              // CommonStore.update(s => {
              //   s.isAuthenticating = false;
              // });
            }

            console.log(`supportCode: ${supportCode}`);

            const supportCodeSnapshot = await firestore()
              .collection(Collections.SupportCode)
              .where('code', '==', supportCode)
              .where('deletedAt', '==', null)
              .where('endDateTime', '>', moment().valueOf())
              .limit(1)
              .get();

            console.log(supportCodeSnapshot);

            if (supportCodeSnapshot && !supportCodeSnapshot.empty) {
              const supportCodeObj = supportCodeSnapshot.docs[0].data();

              // valid support code found

              if (moment().isBefore(supportCodeObj.startDateTime)) {
                Alert.alert('Login failed', "This support code still haven't unlocked yet.", [
                  { text: "OK", onPress: () => { setLoadingModal(false) } }
                ],
                  { cancelable: false });

                CommonStore.update(s => {
                  s.isAuthenticating = false;
                });

                return;
              }

              let userAnonymous = res.user;
              let firebaseToken = await userAnonymous.getIdToken();
              ApiClient.GET(`${API.getTokenForSupport + firebaseToken}&app=${APP_TYPE.MERCHANT}&code=${supportCode}`).then(async (result) => {
                // setLoading(false);              

                if (result && result.merchantId) {
                  if (result && result.token) {
                    Token.setToken(result.token);
                    Token.setRefreshToken(result.refreshToken);

                    await AsyncStorage.setItem('accessToken', result.token ? result.token : '');

                    ApiClient.GET(API.userAdminForSupport + supportCode).then(async (userData) => {
                      CommonStore.update(s => {
                        s.supportCodeData = (userData && userData.supportCode) ? userData.supportCode : null;
                      });

                      await AsyncStorage.setItem('supportCodeData', (userData && userData.supportCode) ? JSON.stringify(userData.supportCode) : '');

                      global.supportCodeData = (userData && userData.supportCode) ? userData.supportCode : null;

                      User.setUserData(userData);
                      User.setName(userData.name);
                      User.setRefreshToken(userData.refreshToken);
                      User.setUserId(userData.firebaseUid);
                      User.setlogin(true);
                      User.setMerchantId(userData.merchantId);
                      User.setOutletId(userData.outletId);

                      if (userData.role === ROLE_TYPE.ADMIN) {
                        await AsyncStorage.setItem(
                          'email',
                          (userData && userData.supportCode && userData.supportCode.clientEmail) ? userData.supportCode.clientEmail : ''
                        );
                        // await AsyncStorage.setItem(
                        //   'password',
                        //   password
                        // );

                        // await AsyncStorage.setItem('last.accessToken', result.token);

                        // await AsyncStorage.setItem(
                        //   'last.userData',
                        //   JSON.stringify(userData)
                        // );

                        // await AsyncStorage.setItem(
                        //   'last.refreshToken',
                        //   userData.refreshToken
                        // );
                      }

                      await AsyncStorage.setItem(
                        'loggedIn',
                        "true"
                      );

                      await AsyncStorage.setItem(
                        'userData',
                        JSON.stringify(userData) ? JSON.stringify(userData) : ''
                      );

                      ////////////////////////////////////

                      await AsyncStorage.setItem(
                        'merchantId',
                        userData.merchantId ? userData.merchantId : ''
                      );

                      await AsyncStorage.setItem(
                        'refreshToken',
                        userData.refreshToken ? userData.refreshToken : ''
                      );

                      await AsyncStorage.setItem(
                        'role',
                        userData.role ? userData.role : ''
                      );

                      await AsyncStorage.setItem(
                        'firebaseUid',
                        userData.firebaseUid ? userData.firebaseUid : ''
                      );

                      if (userData.isAlphaUser) {
                        await AsyncStorage.setItem(
                          'isAlphaUser',
                          '1'
                        );
                      }
                      else {
                        await AsyncStorage.setItem(
                          'isAlphaUser',
                          '0'
                        );
                      }

                      if (userData.isBetaUser) {
                        await AsyncStorage.setItem(
                          'isBetaUser',
                          '1'
                        );
                      }
                      else {
                        await AsyncStorage.setItem(
                          'isBetaUser',
                          '0'
                        );
                      }

                      await AsyncStorage.setItem(
                        'privileges',
                        JSON.stringify(userData.privileges ? userData.privileges : [])
                      );

                      await AsyncStorage.setItem(
                        'screensToBlock',
                        JSON.stringify(userData.screensToBlock ? userData.screensToBlock : [])
                      );

                      await AsyncStorage.setItem(
                        'currOutletId',
                        userData.outletId ? userData.outletId : '',
                      );

                      await AsyncStorage.setItem(
                        'isMasterAccount',
                        userData.isMasterAccount !== undefined ?
                          (userData.isMasterAccount ? '1' : '0') :
                          '1',
                      );

                      global.currUserRole = userData.role;

                      UserStore.update(s => {
                        s.firebaseUid = userData.firebaseUid;
                        s.merchantId = userData.merchantId;

                        s.role = userData.role;

                        s.isAlphaUser = userData.isAlphaUser ? true : false;

                        s.isBetaUser = userData.isBetaUser ? true : false;

                        // Email login no need first
                        // s.privileges = userData.privileges;
                      });

                      MerchantStore.update(s => {
                        s.currOutletId = userData.outletId;
                      });

                      ////////////////////////////////////

                      // ApiClient.GET(API.outlet2 + User.getOutletId()).then((result) => {
                      //   User.setName(result.name);
                      //   User.setQueueStatus(result.queueStatus);
                      //   User.setRefreshCurrentAction(result.reservationStatus);
                      //   checkLogin(true)
                      // });

                      CommonStore.update(s => {
                        s.isAuthenticating = false;
                      });

                      // checkLogin(true);                    

                      // clock in 
                      // let dateTime = Date.now();

                      // let body = {
                      //   employeeId: userData.firebaseUid,
                      //   loginTime: dateTime,

                      //   merchantId: userData.merchantId,
                      //   outletId: userData.outletId,
                      // }

                      // ApiClient.POST(API.updateUserClockInOut, body).then((result) => {
                      //   // console.log('updateUserClockIn', result);
                      // });

                      switchLogin();
                    });
                  }
                  else {
                    Alert.alert('Login failed', "Invalid merchant account", [
                      { text: "OK", onPress: () => { setLoadingModal(false) } }
                    ],
                      { cancelable: false });

                    CommonStore.update(s => {
                      s.isAuthenticating = false;
                    });
                  }
                } else {
                  Alert.alert('Login failed', "Invalid merchant account", [
                    { text: "OK", onPress: () => { setLoadingModal(false) } }
                  ],
                    { cancelable: false });

                  CommonStore.update(s => {
                    s.isAuthenticating = false;
                  });
                }
              });
            }
            else {
              Alert.alert('Login failed', "No matched support code found, please try another one.", [
                { text: "OK", onPress: () => { setLoadingModal(false) } }
              ],
                { cancelable: false });

              CommonStore.update(s => {
                s.isAuthenticating = false;
              });
            }
          } catch (error) {
            Alert.alert('Login failed', "Unable to commnunicate with the server, please try again.", [
              { text: "OK", onPress: () => { setLoadingModal(false) } }
            ],
              { cancelable: false });
            // setState({ loading: false }); test herks

            CommonStore.update(s => {
              s.isAuthenticating = false;
            });
          }
        }
        else {
          Alert.alert('Info', 'Device is offline now, please connect to the Internet first');

          setLoadingModal(false);

          CommonStore.update(s => {
            s.isAuthenticating = false;
          });
        }
      });
    }
  }


  // function end

  var scaleContainer = {};
  if (
    // switchMerchant
    simulateTabletMode
  ) {
    scaleContainer = {
      ...getTransformForScreenOutsideNavigation(),
    };
  }

  return (
    <View style={[styles.container, scaleContainer]}>
      <View
        style={{
          paddingHorizontal: 30,
          flex: 1,
          justifyContent: 'center',
          top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 0.4 : 0,
        }}>
        {/* <ModalView
          style={{ flex: 1, zIndex: -1 }}
          visible={loadingModal}
          transparent={true}
          animationType={'slide'}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalViewImport}>
              <ActivityIndicator color={Colors.whiteColor} size={'large'} />
            </View>
          </View>
        </ModalView> */}
        {reset == false ? (
          <KeyboardAvoidingView behavior='padding' style={{ alignSelf: 'center', width: '65%' }}>
            <Image
              style={[styles.logo, switchMerchant ? {
                transform: [
                  { scaleX: 0.7 },
                  { scaleY: 0.7 }
                ],
                height: 50,
              } : {}]}
              resizeMode="contain"
              source={require('../assets/image/logo_2.png')}
            />
            <Text style={[styles.logoTxt, switchMerchant ? {
              transform: [
                { scaleX: 0.6 },
                { scaleY: 0.6 }
              ],
              width: windowWidth,
              textAlign: 'center',
              marginTop: 0,
              marginBottom: switchMerchant ? 0 : 10,
              fontSize: 16,
            } : {}]}>Unlimited Perks</Text>
            <Text style={[styles.loginTxt, switchMerchant ? {
              fontSize: 14,
            } : {}]}>Login</Text>
            <View style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }}>
              <Text style={[styles.description, switchMerchant ? {
                paddingVertical: 5,
                fontSize: 10,
              } : {}]}>Please login to continue</Text>

              <TouchableOpacity style={{
                marginLeft: 10,
              }}
                onPress={async () => {
                  // await AsyncStorage.removeItem('isTabletForce');

                  // const isTabletForce = await AsyncStorage.getItem('isTabletForce');
                  // const isTabletForce = storageMMKV.getString('isTabletForce');
                  const isTabletForce = await global.watermelonDBDatabase.localStorage.get('isTabletForce');

                  if (isTabletForce === '1' ||
                    isTabletForce === '0') {
                    // await AsyncStorage.setItem('isTabletForce', isTabletForce === '1' ? '0' : '1');
                    // storageMMKV.set('isTabletForce', isTabletForce === '1' ? '0' : '1');
                    await global.watermelonDBDatabase.localStorage.set('isTabletForce', isTabletForce === '1' ? '0' : '1');
                    global.isTabletForce = isTabletForce === '1' ? '0' : '1';

                    // CommonStore.update(s => {
                    //   s.switchMerchant = isTabletForce === '1' ? true : false;
                    // });

                    CommonStore.update(s => {
                      s.switchMerchant = false;
                    });

                    // DevSettings.reload();

                    Alert.alert('Info', 'Please close the app and open again for the changes to take effect.');
                  }
                  else {
                    var valueToSet = isTablet();

                    // await AsyncStorage.setItem('isTabletForce', valueToSet ? '0' : '1');
                    // storageMMKV.set('isTabletForce', valueToSet === '1' ? '0' : '1');
                    await global.watermelonDBDatabase.localStorage.set('isTabletForce', valueToSet === '1' ? '0' : '1');
                    global.isTabletForce = valueToSet ? '0' : '1';

                    // CommonStore.update(s => {
                    //   s.switchMerchant = valueToSet ? true : false;
                    // });

                    CommonStore.update(s => {
                      s.switchMerchant = false;
                    });

                    // DevSettings.reload();

                    Alert.alert('Info', 'Please close the app and open again for the changes to take effect.');
                  }
                }}
              >
                <Entypo
                  name="tablet-mobile-combo"
                  size={isTablet() ? 14 : 14}
                  color={Colors.primaryColor} />
              </TouchableOpacity>

              <Text style={[styles.description, switchMerchant ? {
                paddingVertical: 5,
                fontSize: 10,
              } : {}, {
                marginLeft: 10,
              }]}>
                {/* {!switchMerchant ? 'Now In Tablet mode' : 'Now In mobile mode'} */}
                {isTablet() ? 'Now In Tablet mode' : 'Now In mobile mode'}
              </Text>
            </View>

            {
              showSupportCodeLogin
                ?
                <>
                  <TextInput
                    editable={!isAuthenticating}
                    underlineColorAndroid={Colors.fieldtBgColor}
                    autoCapitalize='none'
                    clearButtonMode="while-editing"
                    style={[styles.textInput, switchMerchant ? {
                      height: windowWidth / 20,
                      marginTop: 5,
                    } : {}]}
                    placeholder="Support Code"
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    secureTextEntry
                    onChangeText={(text) => {
                      setSupportCode(text.trim());
                    }}
                    value={supportCode}
                  />
                </>
                :
                <>
                  <TextInput
                    testID="LoginScreen.textInputEmail"
                    editable={!isAuthenticating}
                    underlineColorAndroid={Colors.fieldtBgColor}
                    clearButtonMode="while-editing"
                    autoCapitalize='none'
                    style={[styles.textInput, switchMerchant ? {
                      height: windowWidth / 20,
                      marginTop: 5,
                      fontSize: 10,
                    } : {}]}
                    placeholder="Email"
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    keyboardType="email-address"
                    onChangeText={(text) => {
                      setEmail(text.trim());
                    }}
                    value={email}
                  />
                  <TextInput
                    testID="LoginScreen.textInputPassword"
                    editable={!isAuthenticating}
                    underlineColorAndroid={Colors.fieldtBgColor}
                    autoCapitalize='none'
                    clearButtonMode="while-editing"
                    style={[styles.textInput, switchMerchant ? {
                      height: windowWidth / 20,
                      marginTop: 5,
                      fontSize: 10,
                    } : {}]}
                    placeholder="Password"
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    secureTextEntry
                    onChangeText={(text) => {
                      setPassword(text.trim());
                    }}
                    value={password}
                  />
                </>
            }
            <TouchableOpacity
              testID="LoginScreen.buttonLogin"
              disabled={isAuthenticating}
              onPress={() => {
                goLogin();
                setLoadingModal(true);

              }}>
              <View style={[Styles.button, { marginTop: 20 }, switchMerchant ? {
                marginTop: 6,
                marginVertical: 10,
                padding: 7
              } : {}]}>
                {isAuthenticating ?
                  <Text style={[{ color: '#ffffff', fontSize: 18 }, switchMerchant ? {
                    fontSize: 14,
                  } : {}]}>
                    LOADING...
                  </Text>
                  :
                  <Text style={[{ color: '#ffffff', fontSize: 18 }, switchMerchant ? {
                    fontSize: 14,
                  } : {}]}>
                    {loadingModal ? 'LOADING...' : 'LOGIN'}
                  </Text>
                }
              </View>
            </TouchableOpacity>

            {/* 2022-07-04 - Hide switch to pin login screen changes */}
            <View style={[styles.resetContainer, {
              marginBottom: 5,
              marginTop: -5,

              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }]}>
              <View style={{
                // width: '50%',
                flexDirection: 'row'
              }}>
                <Text style={[{ color: Colors.fieldtTxtColor, marginRight: 5 }, switchMerchant ? {
                  marginTop: switchMerchant ? 0 : 5,
                  fontSize: 10,
                } : {}]}>
                  Switch Login?{' '}
                </Text>
                <TouchableOpacity
                  onPress={() => {
                    // switchLogin();
                    changeToPinLogin();

                    global.isOnLoginPage = false;
                    global.isOnPinLoginPage = true;
                  }}>
                  <Text style={[{ color: Colors.primaryColor }, switchMerchant ? {
                    marginTop: switchMerchant ? 0 : 5,
                    fontSize: 10,
                  } : {}]}>Pin</Text>
                </TouchableOpacity>
              </View>

              <View style={{
                // width: '50%',
                marginLeft: showSupportCodeLogin ? '5%' : '20%',
                flexDirection: 'row'
              }}>
                <Text style={[{ color: Colors.fieldtTxtColor, marginRight: 5 }, switchMerchant ? {
                  marginTop: switchMerchant ? 0 : 5,
                  fontSize: 10,
                } : {}]}>
                  {showSupportCodeLogin ? 'Business Account' : 'Supporting'}?{' '}
                </Text>
                <TouchableOpacity
                  onPress={() => {
                    // switchLogin();
                    // changeToPinLogin();
                    setShowSupportCodeLogin(!showSupportCodeLogin);
                  }}>
                  <Text style={[{ color: Colors.primaryColor }, switchMerchant ? {
                    marginTop: switchMerchant ? 0 : 5,
                    fontSize: 10,
                  } : {}]}>{showSupportCodeLogin ? 'Email' : 'Code'}</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              marginRight: 5,
            }}>
              <Text style={[{ color: Colors.fieldtTxtColor }, switchMerchant ? {
                marginTop: switchMerchant ? 0 : 5,
                fontSize: 10,
              } : {}]}>
                Forgot Password?{' '}
              </Text>
              <TouchableOpacity
                onPress={() => {
                  setReset(!reset);
                }}>
                <Text style={[{ color: Colors.primaryColor }, switchMerchant ? {
                  marginTop: switchMerchant ? 0 : 5,
                  fontSize: 10,
                } : {}]}>Reset it</Text>
              </TouchableOpacity>
            </View>
          </KeyboardAvoidingView>
        ) : (
          <View style={{ alignSelf: 'center', width: '65%' }}>
            <Image
              style={styles.logo}
              resizeMode="contain"
              source={require('../assets/image/logo_2.png')}
            />
            <Text style={styles.logoTxt}>Unlimited Perks</Text>
            <Text style={styles.loginTxt}>RESET PASSWORD</Text>
            <Text style={styles.description}>
              The temporary password will be sent to your email
            </Text>
            <TextInput
              editable={!isAuthenticating}
              underlineColorAndroid={Colors.fieldtBgColor}
              placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
              clearButtonMode="while-editing"
              style={styles.textInput}
              placeholder="Email"
              keyboardType="email-address"
              onChangeText={(text) => {
                setEmail(text.trim());
              }}
              value={email}
            />
            <TouchableOpacity
              disabled={isAuthenticating}
              onPress={() => {
                goReset();
              }}>
              <View style={[Styles.button, { marginTop: 50 }]}>
                <Text style={{ color: '#ffffff', fontSize: 18 }}>
                  {isAuthenticating
                    ? 'LOADING...'
                    : 'SEND RESET LINK TO EMAIL'}
                </Text>
              </View>
            </TouchableOpacity>
            <View style={styles.resetContainer}>
              <Text style={{ color: Colors.fieldtTxtColor }}>
                Already have account??{' '}
              </Text>
              <TouchableOpacity
                onPress={() => {
                  setReset(!reset);
                }}>
                <Text style={{ color: Colors.primaryColor }}>Login</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>

      <View style={{ flex: 1 }}>
        <Image
          style={[styles.loginImg, switchMerchant ? {
            height: windowWidth,
          } : {}]}
          resizeMode="cover"
          source={require('../assets/image/loginImg.png')}
        />

        {/* {switchMerchant &&
          <View style={[styles.switchContainer, {
            bottom: windowHeight * 0.05,
          }]}>
            <Switch
              onSyncPress={
                async (value) => {
                  CommonStore.update(s => {
                    s.switchMerchant = false;
                  });

                  await AsyncStorage.setItem('switchMerchant', '0');
                }
              }
              // onChange={() => this.switchQueueStatus(this)}
              value={switchMerchant}
              circleColorActive={Colors.primaryColor}
              circleColorInactive={Colors.fieldtTxtColor}
              backgroundActive='#dddddd'
            />
          </View>
        } */}
      </View>

      {/* <View>
        <Switch
          onSyncPress={(value) =>
            this.setState({ switch: value })
          }
          // onChange={() => this.switchQueueStatus(this)}
          value={this.state.switch}
          circleColorActive={Colors.primaryColor}
          circleColorInactive={Colors.fieldtTxtColor}
          backgroundActive='#dddddd'
        />
      </View> */}
    </View >
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
  },
  logo: {
    width: 300,
    height: 67,
    alignSelf: 'center',
    marginTop: 10,
  },
  logoTxt: {
    color: Colors.descriptionColor,
    fontSize: 20,
    letterSpacing: 7,
    marginTop: 10,
    alignSelf: 'center',
    marginBottom: 40,
  },
  loginTxt: {
    color: Colors.mainTxtColor,
    fontWeight: '600',
    fontSize: 30,
  },
  description: {
    color: Colors.descriptionColor,
    paddingVertical: 10,
  },
  textInput: {
    padding: 5,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingLeft: 10,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginTop: 20,
  },
  checkBox: {
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: Colors.descriptionColor,
    width: 30,
    height: 30,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  floatbtn: {
    zIndex: 1,
    position: 'absolute',
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 60 / 2,
    backgroundColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  loginImg: {
    width: undefined,
    height: '100%',
    resizeMode: 'cover',
  },

  switchContainer: {
    position: 'absolute',
    bottom: 0,
    display: 'flex',
    alignItems: 'center',
    width: '100%',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
    // borderRadius: 12
  },
  modalView: {
    height: Dimensions.get('window').width * 0.2,
    width: Dimensions.get('window').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center'
  },
});
export default LoginScreen;
