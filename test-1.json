{
    "label": "<PERSON> <PERSON> San (SS15)",
    "value": "a4d35ccf-af9c-4cc9-9ae4-2f6203bb6c52"
}
 LOG  [undefined
]
 LOG  {
    "activeItemStyle": {},
    "activeLabelStyle": {},
    "arrowColor": "black",
    "arrowSize": 20,
    "arrowStyle": {
        "fontWeight": "bold"
    },
    "autoScrollToDefaultValue": false,
    "containerProps": {},
    "containerStyle": {
        "height": 40,
        "marginLeft": 10
    },
    "controller": [Function controller
    ],
    "customArrowDown": [Function customArrowDown
    ],
    "customArrowUp": [Function customArrowUp
    ],
    "customTickIcon": [Function customTickIcon
    ],
    "defaultValue": [
        "a4d35ccf-af9c-4cc9-9ae4-2f6203bb6c52"
    ],
    "disabled": false,
    "dropDownMaxHeight": 100,
    "dropDownStyle": {
        "backgroundColor": "#F7F7F7",
        "borderRadius": 10,
        "borderWidth": 1,
        "height": 90,
        "textAlign": "left",
        "width": 210,
        "zIndex": 2
    },
    "globalTextStyle": {
        "color": "#27353C",
        "fontFamily": "NunitoSans-SemiBold",
        "fontSize": 14,
        "marginLeft": 5
    },
    "isVisible": false,
    "itemStyle": {
        "justifyContent": "flex-start",
        "zIndex": 2
    },
    "items": [
        {
            "label": "Ho Min San (SS15)",
            "value": "a4d35ccf-af9c-4cc9-9ae4-2f6203bb6c52"
        },
        {
            "label": "Ho Min San (TTDI)",
            "value": "b422c1d9-d30b-4de7-ad49-2e601d950919"
        },
        {
            "label": "Ho Min San (Mid Valley)",
            "value": "b8edae70-30b4-4356-aaa4-f8dc0d664818"
        }
    ],
    "labelLength": 1000,
    "labelProps": {},
    "labelStyle": {},
    "max": 10000000,
    "min": 0,
    "multiple": true,
    "multipleText": "%d outlet(s) selected",
    "onChangeItem": [Function onChangeItem
    ],
    "onChangeList": [Function onChangeList
    ],
    "onClose": [Function onClose
    ],
    "onOpen": [Function onOpen
    ],
    "onSearch": [Function onSearch
    ],
    "placeholder": "Choose Outlet",
    "placeholderStyle": {
        "color": "#ACACAC"
    },
    "scrollViewProps": {},
    "searchTextInputProps": {},
    "searchable": false,
    "searchableError": [Function searchableError
    ],
    "searchablePlaceholder": "Search for an item",
    "searchablePlaceholderTextColor": "gray",
    "searchableStyle": {},
    "selectedLabelLength": 1000,
    "selectedLabelStyle": {},
    "showArrow": true,
    "style": {
        "backgroundColor": "#F7F7F7",
        "borderRadius": 10,
        "paddingVertical": 0,
        "width": 210
    },
    "zIndex": 5000
}